---
description: 
globs: 
alwaysApply: false
---
# Development Server Management Best Practices

## Port Management and Process Control

### ✅ DO: Follow the Port Conflict Resolution Protocol

1. **Stop Current Servers First**
   ```bash
   # Use Ctrl+C to stop running servers
   # Or kill all Node.js processes if needed
   taskkill /F /IM node.exe
   ```

2. **Check Port Usage**
   ```bash
   # Check specific port (e.g., 4000)
   netstat -ano | findstr :4000
   ```

3. **Kill Specific Process**
   ```bash
   # Kill by PID
   taskkill /F /PID <process_id>
   ```

4. **Verify Port is Free**
   ```bash
   # Run netstat again to confirm
   netstat -ano | findstr :4000
   ```

### ❌ DON'T: Use Port Number Changes as Workarounds

```typescript
// ❌ DON'T: Change ports as a quick fix
{
  "scripts": {
    "dev": "vite --port 4001" // Wrong: Using different port to avoid conflict
  }
}

// ✅ DO: Keep consistent port configuration
{
  "scripts": {
    "dev": "vite --port 4000" // Correct: Using project's standard port
  }
}
```

## Configuration Consistency

### ✅ DO: Maintain Consistent Port Configuration

1. **In package.json**
   ```json
   {
     "scripts": {
       "dev": "vite --port 4000"
     }
   }
   ```

2. **In vite.config.ts**
   ```typescript
   export default defineConfig({
     server: {
       port: 4000,
       strictPort: true,
       hmr: {
         clientPort: 4000
       }
     }
   });
   ```

3. **In environment variables (.env)**
   ```env
   VITE_DEV_SERVER_PORT=4000
   ```

### ❌ DON'T: Mix Port Numbers Across Configuration Files

```typescript
// ❌ DON'T: Use inconsistent ports
// package.json
{
  "dev": "vite --port 4000"
}

// vite.config.ts
export default defineConfig({
  server: {
    port: 4001 // Wrong: Different from package.json
  }
});
```

## Process Management

### ✅ DO: Proper Process Cleanup

1. **Before Starting New Server**
   - Stop existing development servers
   - Check for and kill orphaned processes
   - Verify ports are available

2. **When Stopping Development**
   - Use proper shutdown signals (Ctrl+C)
   - Verify all related processes are terminated
   - Check port availability before next startup

### ❌ DON'T: Leave Orphaned Processes

- Don't close terminal windows without properly stopping servers
- Don't start new servers without checking for existing processes
- Don't ignore port conflict errors

## Troubleshooting Steps

1. **When Port Conflict Occurs**
   ```bash
   # 1. Check what's using the port
   netstat -ano | findstr :4000
   
   # 2. Kill the process
   taskkill /F /PID <process_id>
   
   # 3. Verify port is free
   netstat -ano | findstr :4000
   
   # 4. Start server with correct configuration
   npm run dev
   ```

2. **When Process Won't Stop**
   ```bash
   # 1. Try normal termination
   Ctrl+C
   
   # 2. If that fails, kill Node processes
   taskkill /F /IM node.exe
   
   # 3. If specific process needed
   taskkill /F /PID <process_id>
   ```

## Best Practices Summary

1. **Always use consistent port numbers** across all configuration files
2. **Clean up processes properly** before starting new ones
3. **Verify port availability** before starting servers
4. **Document standard ports** in project documentation
5. **Use proper shutdown procedures** when stopping development
6. **Monitor process and port usage** during development

## Common Issues and Solutions

| Issue | Solution |
|-------|----------|
| Port conflict | Follow Port Conflict Resolution Protocol |
| Orphaned processes | Use `taskkill` to clean up |
| Inconsistent ports | Audit and align configuration files |
| HMR not working | Verify HMR port matches server port |
| Multiple instances | Stop all instances, clean up, restart |

Remember: Proper process management is crucial for efficient development workflow and preventing resource conflicts.

