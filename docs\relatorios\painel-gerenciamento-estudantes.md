# Relatório: Painel de Gerenciamento de Estudantes para Dashboard dos Pais

## 📋 Visão Geral do Projeto

Este relatório analisa a implementação de um novo painel centralizado para gerenciamento de estudantes dentro do dashboard dos pais na plataforma Locate-Family-Connect. O painel proposto na rota `/parent-dashboard/students` permitirá que responsáveis gerenciem estudantes vinculados de forma mais eficiente e organizada.

## 🔍 Análise da Situação Atual

Atualmente, o dashboard dos pais apresenta as seguintes funcionalidades:

- **Menu lateral** com navegação para diferentes áreas
- **Lista de estudantes vinculados** (<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>)
- **Visualização de mapa** para localização de estudantes selecionados
- **Histórico de localizações** com detalhes como precisão, endereço e timestamp
- **Botão "Adicionar Estudante"** com funcionalidade limitada

No entanto, as funções de gerenciamento estão fragmentadas e não há um local centralizado para editar perfis, configurar permissões ou gerenciar notificações.

## 🛠️ Solução Proposta: Painel de Gerenciamento de Estudantes

### Funcionalidades Principais:

1. **Gerenciamento Centralizado de Estudantes**
   - Adicionar novos estudantes
   - Editar perfis existentes (nome, escola, série, turma)
   - Remover estudantes vinculados

2. **Configuração de Permissões por Estudante**
   - Horários específicos de monitoramento
   - Configuração de geocercas (áreas seguras/áreas de alerta)
   - Controle de compartilhamento de localização

3. **Gerenciamento de Notificações**
   - Alertas de entrada/saída de geocercas
   - Notificações de bateria baixa
   - Alertas de inatividade

## 👨‍💻 Componentes Necessários

Com base na análise do código existente do `StudentManagementPanel.tsx`, identificamos os seguintes componentes:

1. **`StudentManagementPanel`**
   - Componente principal que integra todas as funcionalidades
   - Gerencia o estado de estudantes selecionados e operações CRUD

2. **`StudentForm`**
   - Formulário para adicionar/editar estudantes
   - Campos para nome, escola, série, turma

3. **`StudentDetails`**
   - Exibe e permite edição de detalhes do estudante
   - Interface para configurações avançadas

4. **`StudentList`**
   - Lista navegável de estudantes vinculados
   - Indicadores visuais de status

5. **Novos Componentes Necessários:**
   - `GeofenceManager` - Gerenciamento de cercas geográficas
   - `NotificationSettings` - Configuração de notificações
   - `MonitoringSchedule` - Definição de horários de monitoramento

## 📊 Serviços e Integrações

O painel precisará integrar-se com:

1. **Serviços de Dados**
   - `student-management-service.ts` para operações CRUD
   - `location-service.ts` para geocercas e histórico

2. **API MapBox**
   - Desenho e configuração de geocercas
   - Visualização de áreas seguras/perigosas

3. **Sistema de Notificações**
   - Configuração de alertas para responsáveis
   - Definição de regras por estudante

## 🔧 Integração com Backend

A implementação exigirá:

1. **Extensões do Backend**
   - Novas tabelas para geocercas e configurações de notificação
   - APIs para gerenciamento de permissões
   - Procedimentos armazenados para regras de monitoramento

2. **Políticas de Segurança**
   - Políticas RLS para garantir que apenas responsáveis autorizados possam gerenciar estudantes
   - Proteção de dados sensíveis

3. **Edge Functions**
   - Processamento de eventos de localização
   - Funções para verificação de regras de geocercas

## ⚠️ Desafios Técnicos Identificados

1. **CORS e Serviço de Geolocalização**
   - Foi identificado um problema CORS com `nominatim.openstreetmap.org` que precisa ser resolvido antes da implementação

2. **Service Worker**
   - Erros no cache-first do service worker afetando recursos de rede

3. **Integração MapBox**
   - Necessidade de implementar desenho de geocercas e processamento de eventos geoespaciais

## 📅 Etapas de Implementação Recomendadas

1. **Fase 1: Estrutura Básica do Painel**
   - Criação da rota `/parent-dashboard/students`
   - Implementação do `StudentManagementPanel` com operações CRUD básicas

2. **Fase 2: Configurações de Monitoramento**
   - Implementação de geocercas
   - Definição de horários de monitoramento

3. **Fase 3: Sistema de Notificações**
   - Configuração de alertas
   - Personalização por estudante

4. **Fase 4: Testes e Otimização**
   - Testes de integração
   - Otimização de desempenho

## 🔐 Considerações de Segurança

1. **Proteção de Dados**
   - Garantir que dados de localização sejam protegidos por RLS
   - Implementar controle de acesso por perfil

2. **Auditoria**
   - Registrar alterações em configurações de monitoramento
   - Manter histórico de permissões

## 🌐 Compatibilidade

A implementação deve:
- Manter compatibilidade com o sistema de autenticação PKCE existente
- Funcionar em dispositivos móveis (design responsivo)
- Integrar-se aos serviços existentes de Resend para notificações por email

## 📝 Próximos Passos

1. Definir protótipos de interface para o novo painel
2. Refinar o modelo de dados para incluir geocercas e configurações de notificação
3. Atualizar as políticas RLS para acomodar novas funcionalidades
4. Implementar a solução para os problemas CORS identificados

---

Este relatório serve como base para a implementação do Painel de Gerenciamento de Estudantes, um componente crítico que aumentará significativamente o valor da plataforma Locate-Family-Connect para os responsáveis.

*Data: 06/06/2025*
*Autor: Equipe de Desenvolvimento*
