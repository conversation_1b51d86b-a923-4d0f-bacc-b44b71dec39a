// Environment configuration with fallbacks for Supabase
export const env = {
  // Supabase Configuration with fallbacks
  VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL || 'https://rsvjnndhbyyxktbczlnk.supabase.co',
  VITE_SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJzdmpubmRoYnl5eGt0YmN6bG5rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM0MDk3NzksImV4cCI6MjA1ODk4NTc3OX0.AlM_iSptGQ7G5qrJFHU9OECu1wqH6AXQP1zOU70L0T4',
  
  // Mapbox configuration with backward compatibility for older env names
  MAPBOX_TOKEN:
    import.meta.env.VITE_MAPBOX_TOKEN ||
    import.meta.env.VITE_MAPBOX_ACCESS_TOKEN ||
    'pk.eyJ1IjoidGVjaC1lZHUtbGFiIiwiYSI6ImNtN3cxaTFzNzAwdWwyanMxeHJkb3RrZjAifQ.h0g6a56viW7evC7P0c5mwQ',
  MAPBOX_STYLE_URL:
    import.meta.env.VITE_MAPBOX_STYLE_URL ||
    import.meta.env.VITE_MAPBOX_STYLE ||
    'mapbox://styles/mapbox/streets-v12',
  MAPBOX_CENTER:
    import.meta.env.VITE_MAPBOX_CENTER ||
    import.meta.env.VITE_MAPBOX_INITIAL_CENTER ||
    (import.meta.env.VITE_MAPBOX_INITIAL_LONGITUDE &&
      import.meta.env.VITE_MAPBOX_INITIAL_LATITUDE
      ? `${import.meta.env.VITE_MAPBOX_INITIAL_LONGITUDE},${import.meta.env.VITE_MAPBOX_INITIAL_LATITUDE}`
      : '-23.5489,-46.6388'), // São Paulo
  MAPBOX_ZOOM:
    import.meta.env.VITE_MAPBOX_ZOOM ||
    import.meta.env.VITE_MAPBOX_INITIAL_ZOOM ||
    '12',
  
  // API URLs  
  API_URL: import.meta.env.VITE_API_URL || '',
  SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL || 'https://rsvjnndhbyyxktbczlnk.supabase.co',
  SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJzdmpubmRoYnl5eGt0YmN6bG5rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM0MDk3NzksImV4cCI6MjA1ODk4NTc3OX0.AlM_iSptGQ7G5qrJFHU9OECu1wqH6AXQP1zOU70L0T4',
  RESEND_API_KEY: import.meta.env.VITE_RESEND_API_KEY || '',
  
  // Email settings
  EMAIL_FROM: import.meta.env.VITE_EMAIL_FROM || '<EMAIL>',
  APP_DOMAIN: import.meta.env.VITE_APP_DOMAIN || 'sistema-monitore.com.br',
  USE_FALLBACK_SENDER: import.meta.env.VITE_USE_FALLBACK_SENDER || 'true',
  
  // Other environment variables
  VITE_APP_ENV: import.meta.env.VITE_APP_ENV || 'development',
  NODE_ENV: import.meta.env.NODE_ENV || 'development',
  
  // Development flags
  isDev: import.meta.env.DEV,
  isProd: import.meta.env.PROD,
  
  // Validation
  get isValid() {
    return !!(this.VITE_SUPABASE_URL && this.VITE_SUPABASE_ANON_KEY);
  }
};

// Validate environment on module load
if (!env.isValid) {
  console.warn('🚨 Supabase environment variables not properly configured');
  console.warn('URL:', env.VITE_SUPABASE_URL ? '✅ Set' : '❌ Missing');
  console.warn('Key:', env.VITE_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing');
} else {
  console.log('✅ Supabase environment configured successfully');
}

// Mapbox configuration with backward compatibility for older env names
export const isMapboxTokenValid = () => {
  const token = env.MAPBOX_TOKEN;
  return token && token.startsWith('pk.') && token.length > 10;
};

export default env;
