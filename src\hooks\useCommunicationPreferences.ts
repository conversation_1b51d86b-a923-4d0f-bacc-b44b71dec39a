
import { useState, useEffect } from 'react';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { supabase } from '@/lib/supabase';

export interface CommunicationPreferences {
  id?: string;
  user_id: string;
  email_notifications: boolean;
  sms_notifications: boolean;
  whatsapp_notifications: boolean;
  location_sharing_enabled: boolean;
  emergency_contact_email?: string | undefined;
  emergency_contact_phone?: string | undefined;
  created_at?: string;
  updated_at?: string;
}

export function useCommunicationPreferences() {
  const { user } = useUser();
  const [preferences, setPreferences] = useState<CommunicationPreferences | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const fetchPreferences = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('communication_preferences')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      if (data) {
        setPreferences(data as CommunicationPreferences);
      } else {
        const defaultPrefs: CommunicationPreferences = {
          user_id: user.id,
          email_notifications: true,
          sms_notifications: false,
          whatsapp_notifications: false,
          location_sharing_enabled: false,
          emergency_contact_email: user.email ?? undefined,
          emergency_contact_phone: user.user_metadata?.phone ?? undefined,
        };

        const { data: inserted } = await supabase
          .from('communication_preferences')
          .insert(defaultPrefs)
          .select()
          .single();

        if (inserted) setPreferences(inserted as CommunicationPreferences);
      }
    } catch (error) {
      console.error('Error fetching communication preferences:', error);
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async (
    newPreferences: Partial<CommunicationPreferences>
  ) => {
    if (!user) return;

    setSaving(true);
    try {
      const updateData = { ...preferences, ...newPreferences, user_id: user.id };

      const { data, error } = await supabase
        .from('communication_preferences')
        .upsert(updateData, { onConflict: 'user_id' })
        .select()
        .single();

      if (error) throw error;

      if (data) setPreferences(data as CommunicationPreferences);
    } catch (error) {
      console.error('Error saving communication preferences:', error);
      throw error;
    } finally {
      setSaving(false);
    }
  };

  const refetch = async () => {
    setLoading(true);
    await fetchPreferences();
  };

  useEffect(() => {
    fetchPreferences();
  }, [user]);

  return {
    preferences,
    loading,
    saving,
    savePreferences,
    refetch,
  };
}
