---
description:
globs:
alwaysApply: false
---
# Gerenciamento de Servidores de Desenvolvimento

- **Verifique se um servidor já está em execução antes de iniciar outro**
  - Execute `npm run dev` apenas se não houver nenhum servidor Vite em execução
  - Verifique portas em uso com `netstat -ano | findstr "808"` (Windows) ou `lsof -i :8080,8081,8082` (Unix)
  - Sempre finalize servidores existentes antes de iniciar novos com `taskkill /F /FI "IMAGENAME eq node.exe"` (Windows)

- **Preserve a estabilidade da configuração**
  - Use `npm run dev:unprotected` apenas quando precisar modificar arquivos protegidos
  - Após modificar configurações críticas, documente as alterações no PR
  - Não modifique diretamente arquivos como:
    ```typescript
    // ✅ DO: Use a configuração centralizada
    import { env } from '@/env';
    
    // ❌ DON'T: Modificar diretamente ou usar hardcoded tokens
    const token = 'pk.test';
    ```

- **Mantenha a compatibilidade web/mobile**
  - Evite padrões de wildcard duplo (`**/*`) na configuração do Vite
  - Use a função external correta para excluir arquivos mobile:
    ```typescript
    // ✅ DO: Use padrões simples para exclusão de arquivos mobile
    external: (id) => {
      return id.includes('/mobile/') || 
             id.startsWith('mobile/') || 
             id.includes('\\mobile\\') ||
             id === 'mobile';
    }
    
    // ❌ DON'T: Usar wildcards duplos que causam erros
    external: ['./mobile/**/*']
    ```

- **Verifique a saúde do ambiente antes de iniciar desenvolvimento**
  - Execute `npm run check-env` para verificar variáveis de ambiente
  - Resolva problemas de configuração antes de iniciar o desenvolvimento
  - Configure todas as variáveis necessárias no arquivo `.env`

- **Diagnostique problemas de servidor corretamente**
  - Se o servidor falhar, verifique os logs em primeiro lugar
  - Use `npm run dev:unprotected` para desenvolvimento sem proteção de arquivos
  - Se encontrar erros 401 do MapBox, verifique o token em `.env`

- **Crie e execute testes para componentes críticos**
  - Implemente testes para componentes críticos como MapBox
  - Execute testes antes de fazer commit de alterações em componentes compartilhados
  - Verifique se as alterações não quebram funcionalidades existentes
