import { fireEvent, render, screen } from '@testing-library/react';
import { DashboardHeader } from '../DashboardHeader';

describe('DashboardHeader', () => {
  it('renders the user name', () => {
    render(<DashboardHeader userName="John" onGoToProfile={() => {}} onSignOut={() => {}} />);
    expect(screen.getByText('John')).toBeInTheDocument();
  });

  it('triggers callbacks when buttons are clicked', () => {
    const onGoToProfile = jest.fn();
    const onSignOut = jest.fn();
    render(<DashboardHeader userName="John" onGoToProfile={onGoToProfile} onSignOut={onSignOut} />);

    fireEvent.click(screen.getByRole('button', { name: /perfil/i }));
    expect(onGoToProfile).toHaveBeenCalled();

    fireEvent.click(screen.getByRole('button', { name: /logout/i }));
    expect(onSignOut).toHaveBeenCalled();
  });
});
