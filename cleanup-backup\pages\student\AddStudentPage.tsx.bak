import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import InviteStudentForm from '@/components/student/InviteStudentForm';
import StudentsListContainer from '@/components/student/StudentsListContainer';
import type { Student } from '@/types/auth';

/**
 * Página de adição de estudantes
 * Seguindo as diretrizes do Locate-Family-Connect:
 * - Máximo de 50 linhas por componente
 * - No máximo duas variáveis de instância
 * - Um nível de indentação por método
 */
const AddStudentPage: React.FC = () => {
  const navigate = useNavigate();
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleStudentAdded = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <PageHeader navigate={navigate} title="Gerenciar Estudantes" />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <StudentFormSection onStudentAdded={handleStudentAdded} />
          <StudentListSection 
            refreshTrigger={refreshTrigger} 
            onStudentUpdated={handleStudentAdded} 
          />
        </div>
      </div>
    </div>
  );
};

// Componente de cabeçalho da página (extraído para reduzir o tamanho do componente principal)
interface PageHeaderProps {
  navigate: (to: number) => void;
  title: string;
}

const PageHeader: React.FC<PageHeaderProps> = ({ navigate, title }) => {
  return (
    <div className="flex items-center mb-6">
      <Button
        variant="ghost"
        className="mr-4"
        onClick={() => navigate(-1)}
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Voltar
      </Button>
      <h1 className="text-2xl font-bold">{title}</h1>
    </div>
  );
};

/**
 * Seção do formulário de adição de estudante
 */
interface StudentFormSectionProps {
  onStudentAdded: () => void;
}

const StudentFormSection: React.FC<StudentFormSectionProps> = ({ onStudentAdded }) => {
  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Adicionar Novo Estudante</h2>
      <div className="bg-white shadow-md rounded-lg p-6">
        <InviteStudentForm onInviteSuccess={onStudentAdded} />
      </div>
    </div>
  );
};

/**
 * Seção da lista de estudantes
 */
interface StudentListSectionProps {
  refreshTrigger: number;
  onStudentUpdated: () => void;
}

const StudentListSection: React.FC<StudentListSectionProps> = ({ refreshTrigger, onStudentUpdated }) => {
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  
  return (
    <div>
      <StudentsListContainer
        key={refreshTrigger}
        onSelectStudent={setSelectedStudent}
        selectedStudent={selectedStudent}
        onStudentUpdated={onStudentUpdated}
      />
    </div>
  );
};

export default AddStudentPage;
