# User Experience and Roles - Locate-Family-Connect

## 👥 Visão Geral dos Perfis

O Locate-Family-Connect é estruturado em torno de dois perfis de usuário principais, cada um com fluxos, permissões e interfaces específicas:

1. **Estudante**: Usuário que compartilha sua localização
2. **Responsável**: Usuário que monitora a localização de estudantes

Esta separação de perfis é fundamental para o modelo de segurança e para a experiência do usuário na plataforma.

## 📱 Perfil do Estudante

### Características Principais

- **Função primária**: Compartilhamento de localização
- **Permissões**: Limitadas ao gerenciamento da própria conta e compartilhamento
- **Foco da interface**: Simplicidade e agilidade para compartilhar localização

### Jornada do Usuário

```mermaid
journey
    title Jornada do Estudante
    section Registro
      Criar conta: 3: Estudante
      Vincular-se a responsáveis: 3: Estudante
    section Uso Diário
      Login: 5: Estudante
      Compartilhar localização: 5: Estudante
      Receber confirmações: 4: Estudante
    section Configurações
      Gerenciar permissões: 2: Estudante
      Configurar notificações: 3: Estudante
```

### Fluxo de Autenticação e Autorização

```typescript
// src/contexts/UnifiedAuthContext.tsx (trecho)
// Lógica específica para estudantes
const handleStudentLogin = async (credentials) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword(credentials);
    
    if (error) throw error;
    
    // Verificar se é um estudante
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', data.user.id)
      .single();
      
    if (profile?.user_type !== 'student') {
      await supabase.auth.signOut();
      throw new Error('Acesso não autorizado. Esta conta não é de estudante.');
    }
    
    return data;
  } catch (error) {
    console.error('Erro no login de estudante:', error);
    throw error;
  }
};
```

### Componentes de Interface

Os componentes específicos para estudantes estão organizados em `src/components/student/`:

- **LocationSharingMap**: Mapa com controles para compartilhamento de localização
- **StudentDashboard**: Painel principal do estudante
- **StudentProfile**: Gerenciamento de perfil
- **GuardiansList**: Lista dos responsáveis vinculados

### Wireframes e Fluxos Visuais

```
[Estudante Login] → [Dashboard do Estudante]
       ↓
[Mapa de Compartilhamento] → [Botão Compartilhar]
       ↓
[Confirmação de Compartilhamento]
```

### Permissões SQL

```sql
-- Permissões para tabela 'locations'
CREATE POLICY "Estudantes podem inserir suas próprias localizações"
ON locations
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = student_id);

-- Permissões para visualizar responsáveis
CREATE POLICY "Estudantes podem ver seus próprios responsáveis"
ON guardians
FOR SELECT
TO authenticated
USING (auth.uid() = student_id);
```

## 👨‍👩‍👧‍👦 Perfil do Responsável

### Características Principais

- **Função primária**: Monitoramento de localizações de estudantes
- **Permissões**: Acesso às localizações dos estudantes vinculados
- **Foco da interface**: Visualização clara e notificações de localizações

### Jornada do Usuário

```mermaid
journey
    title Jornada do Responsável
    section Registro
      Criar conta: 3: Responsável
      Vincular estudantes: 4: Responsável
    section Monitoramento
      Login: 5: Responsável
      Verificar localizações: 5: Responsável
      Receber notificações: 4: Responsável
    section Gerenciamento
      Configurar geocercas: 3: Responsável
      Gerenciar vínculos: 3: Responsável
```

### Fluxo de Autenticação e Autorização

```typescript
// src/contexts/UnifiedAuthContext.tsx (trecho)
// Lógica específica para responsáveis
const handleGuardianLogin = async (credentials) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword(credentials);
    
    if (error) throw error;
    
    // Verificar se é um responsável
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', data.user.id)
      .single();
      
    if (profile?.user_type !== 'guardian') {
      await supabase.auth.signOut();
      throw new Error('Acesso não autorizado. Esta conta não é de responsável.');
    }
    
    return data;
  } catch (error) {
    console.error('Erro no login de responsável:', error);
    throw error;
  }
};
```

### Componentes de Interface

Os componentes específicos para responsáveis estão organizados em `src/components/guardian/`:

- **GuardianDashboard**: Painel principal do responsável
- **StudentLocationMap**: Mapa mostrando localizações dos estudantes
- **GeofenceEditor**: Editor de geocercas
- **NotificationCenter**: Centro de notificações

### Wireframes e Fluxos Visuais

```
[Responsável Login] → [Dashboard do Responsável]
       ↓
[Lista de Estudantes] → [Seleção de Estudante]
       ↓
[Mapa de Localização] ← [Centro de Notificações]
       ↓
[Configuração de Geocercas]
```

### Permissões SQL

```sql
-- Permissões para tabela 'locations'
CREATE POLICY "Responsáveis podem ver localizações de seus estudantes"
ON locations
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM guardians
    WHERE guardians.guardian_id = auth.uid()
    AND guardians.student_id = locations.student_id
  )
);

-- Permissões para gerenciar geocercas
CREATE POLICY "Responsáveis podem criar geocercas para seus estudantes"
ON geofences
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM guardians
    WHERE guardians.guardian_id = auth.uid()
    AND guardians.student_id = geofences.student_id
  )
);
```

## 🔄 Fluxo de Vinculação Entre Perfis

### Processo de Vinculação

1. **Convite por email**:
   ```typescript
   // src/lib/invitations.ts
   export const sendGuardianInvitation = async (studentId, guardianEmail) => {
     try {
       const { data, error } = await supabase.functions.invoke('send-invitation', {
         body: { studentId, guardianEmail, type: 'guardian_invitation' }
       });
       
       if (error) throw error;
       return data;
     } catch (error) {
       console.error('Error sending invitation:', error);
       throw error;
     }
   };
   ```

2. **Aceitação do convite**:
   ```typescript
   // src/lib/invitations.ts
   export const acceptGuardianInvitation = async (invitationToken) => {
     try {
       const { data, error } = await supabase.functions.invoke('accept-invitation', {
         body: { invitationToken }
       });
       
       if (error) throw error;
       return data;
     } catch (error) {
       console.error('Error accepting invitation:', error);
       throw error;
     }
   };
   ```

3. **Registro no banco de dados**:
   ```sql
   -- Trigger para registro de vínculo após aceitação
   CREATE OR REPLACE FUNCTION process_invitation_acceptance()
   RETURNS TRIGGER AS $$
   BEGIN
     INSERT INTO guardians (student_id, guardian_id)
     VALUES (NEW.student_id, NEW.guardian_id);
     
     RETURN NEW;
   END;
   $$ LANGUAGE plpgsql;
   ```

## 🎨 Design System e UI/UX

### Princípios de Design

1. **Clareza**: Interfaces diretas que comunicam propósito sem ambiguidade
2. **Acessibilidade**: Conformidade com WCAG 2.1 nível AA
3. **Responsividade**: Funcionamento em diversos dispositivos e tamanhos de tela
4. **Consistência**: Padrões visuais uniformes entre diferentes áreas do aplicativo

### Componentes UI Principais

```typescript
// src/components/ui/Button.tsx
import React from 'react';
import clsx from 'clsx';

export type ButtonVariant = 'primary' | 'secondary' | 'danger';
export type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  isLoading?: boolean;
  fullWidth?: boolean;
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  isLoading = false,
  fullWidth = false,
  className,
  children,
  ...props
}) => {
  return (
    <button
      className={clsx(
        'rounded-md font-medium transition-colors',
        {
          'bg-blue-600 text-white hover:bg-blue-700': variant === 'primary',
          'bg-gray-200 text-gray-800 hover:bg-gray-300': variant === 'secondary',
          'bg-red-600 text-white hover:bg-red-700': variant === 'danger',
          'py-1 px-2 text-sm': size === 'sm',
          'py-2 px-4 text-base': size === 'md',
          'py-3 px-6 text-lg': size === 'lg',
          'w-full': fullWidth,
          'opacity-70 cursor-not-allowed': isLoading || props.disabled,
        },
        className
      )}
      {...props}
    >
      {isLoading ? (
        <span className="flex items-center justify-center">
          <svg className="animate-spin h-5 w-5 mr-2" viewBox="0 0 24 24">
            {/* Spinner SVG */}
          </svg>
          Carregando...
        </span>
      ) : (
        children
      )}
    </button>
  );
};
```

### Sistema de Cores

```css
/* src/styles/colors.css */
:root {
  /* Cores principais */
  --primary-50: #e6f0ff;
  --primary-100: #bddaff;
  --primary-500: #3b82f6;
  --primary-700: #1d4ed8;
  
  /* Alertas e notificações */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  
  /* Neutros */
  --neutral-50: #f9fafb;
  --neutral-100: #f3f4f6;
  --neutral-500: #6b7280;
  --neutral-900: #111827;
}
```

### Adaptações por Perfil

1. **Estudante**:
   - Cores mais vibrantes e joviais
   - Botões grandes e intuitivos
   - Menos densidade de informação

2. **Responsável**:
   - Layout mais informativo e detalhado
   - Ferramentas de filtragem e organização
   - Recursos de personalização

## 📊 Métricas de UX e Feedback

### Métricas Chave

1. **Tempo médio de compartilhamento**: Quanto tempo leva para um estudante completar o processo de compartilhamento
2. **Taxa de conclusão**: Percentual de tentativas de compartilhamento concluídas com sucesso
3. **NPS por perfil**: Net Promoter Score separado para estudantes e responsáveis

### Coleta de Feedback

```typescript
// src/components/shared/FeedbackForm.tsx
import React, { useState } from 'react';
import { Button } from '../ui/Button';
import { TextArea } from '../ui/TextArea';
import { Rating } from '../ui/Rating';
import { useSupabase } from '../../hooks/useSupabase';

export const FeedbackForm: React.FC = () => {
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { supabase, user } = useSupabase();
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      const { error } = await supabase.from('feedback').insert({
        user_id: user?.id,
        rating,
        comment,
      });
      
      if (error) throw error;
      
      // Reset form
      setRating(0);
      setComment('');
      alert('Obrigado pelo feedback!');
    } catch (error) {
      console.error('Error submitting feedback:', error);
      alert('Não foi possível enviar seu feedback. Tente novamente mais tarde.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <h3 className="text-lg font-semibold">Sua opinião é importante!</h3>
      
      <div>
        <label className="block mb-2">Como você avalia sua experiência?</label>
        <Rating value={rating} onChange={setRating} />
      </div>
      
      <div>
        <label htmlFor="comment" className="block mb-2">Comentários (opcional)</label>
        <TextArea
          id="comment"
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          placeholder="Compartilhe suas sugestões ou problemas encontrados"
        />
      </div>
      
      <Button type="submit" isLoading={isSubmitting}>
        Enviar Feedback
      </Button>
    </form>
  );
};
```

### Ciclo de Melhoria Contínua

1. **Coleta de dados**: Via formulários de feedback e métricas de uso
2. **Análise**: Identificação de padrões e pontos de atrito
3. **Priorização**: Foco em melhorias de maior impacto
4. **Implementação**: Aplicação das mudanças necessárias
5. **Verificação**: Confirmação da eficácia das melhorias

## 🛣️ Roadmap de Melhorias

### Curto Prazo (1-3 meses)

- Otimização das telas de compartilhamento para estudantes
- Melhorias na visualização de histórico para responsáveis
- Implementação de tutoriais interativos para novos usuários

### Médio Prazo (3-6 meses)

- Modo escuro para ambos os perfis
- Personalização da interface por usuário
- Melhorias de acessibilidade (contraste, leitores de tela)

### Longo Prazo (6-12 meses)

- Redesenho completo da experiência de notificações
- Implementação de dashboard analítico para responsáveis
- Suporte a múltiplos idiomas

## 📚 Referências

- [Wireframes e Protótipos](docs/wireframes/)
- [Guia de Estilo](docs/style-guide.md)
- [Pesquisa de Usuários](docs/user-research.md)
- [Lei Geral de Proteção de Dados (LGPD)](https://www.planalto.gov.br/ccivil_03/_ato2015-2018/2018/lei/l13709.htm)
- [Web Content Accessibility Guidelines (WCAG)](https://www.w3.org/WAI/standards-guidelines/wcag/)
