---
description: 
globs: 
alwaysApply: true
---
# Padrões de Uso do Mapbox

- **Sempre use a configuração centralizada**
  - Importe a configuração do Mapbox da fonte centralizada:
  ```typescript
  import { configureMapbox, mapboxConfig } from '@/lib/map/mapbox-config';
  ```
  - Use `configureMapbox()` na inicialização do mapa antes de criar a instância
  - Utilize as propriedades do objeto `mapboxConfig` para configurações padrão

- **Validação de dados geográficos**
  - ✅ **SEMPRE** valide coordenadas antes de usá-las:
  ```typescript
  if (
    typeof location.latitude !== 'number' || 
    typeof location.longitude !== 'number' ||
    isNaN(location.latitude) || 
    isNaN(location.longitude)
  ) {
    console.warn('[ComponentName] Invalid coordinates:', location);
    return; // Ou trate adequadamente
  }
  ```
  - ❌ **NÃO** confie que as coordenadas são sempre válidas ou no formato correto

- **Tratamento de erros**
  - Adicione um event listener para o evento 'error' do mapa:
  ```typescript
  map.current.on('error', (e) => {
    console.error('[ComponentName] Map error:', e);
    // Tratar o erro adequadamente
  });
  ```
  - Use try/catch para tratar erros de inicialização do mapa
  - Forneça feedback visual ao usuário em caso de erro

- **Limpeza de recursos**
  - Sempre remova o mapa no cleanup do useEffect:
  ```typescript
  return () => {
    if (map.current) {
      map.current.remove();
      map.current = null;
    }
  };
  ```
  - Remova markers e outros recursos explicitamente antes de adicionar novos

- **Acessibilidade**
  - Adicione texto alternativo para elementos do mapa
  - Garanta que a navegação por teclado funciona corretamente
  - Use cores com contraste adequado para marcadores e controles

- **Desempenho**
  - Use `map.current?.` para prevenir erros de referência nula
  - Limite o número de marcadores exibidos simultaneamente (use clustering para muitos pontos)
  - Considere lazy loading para o mapa em componentes que não são imediatamente visíveis

- **Padrões de Componente**
  - Siga uma estrutura consistente:
    1. Refs e state no topo
    2. useEffect para inicialização do mapa
    3. useEffect separado para atualização de marcadores
    4. Handlers para interações do usuário
    5. JSX com estrutura consistente

- **Logging**
  - Use um padrão consistente para logging:
  ```typescript
  console.log('[ComponentName] Action description:', dataObject);
  ```
  - Log níveis apropriados: info para ações normais, warn para problemas recuperáveis, error para falhas

- **Testes**
  - Teste unitário para componentes de mapa (mock do Mapbox)
  - Teste de integração para verificar se o mapa é exibido corretamente
  - Teste de E2E para fluxos que dependem do mapa

## Referências

- [Documentação Oficial do Mapbox GL JS](mdc:https:/docs.mapbox.com/mapbox-gl-js/api)
- [Documentação de Solução de Problemas](mdc:MAP_TROUBLESHOOTING.md)
- [Exemplos de Implementação](mdc:src/components/guardian/StudentLocationMap.tsx)

