# Deploy and Environment Setup - Locate-Family-Connect

## 🚀 Visão Geral do Ambiente

O Locate-Family-Connect utiliza uma arquitetura baseada em Supabase (backend) e React+Vite (frontend), com ferramentas auxiliares para deploy, monitoramento e desenvolvimento local.

## 🛠️ Requisitos de Sistema

- **Node.js**: v18+ (LTS recomendado)
- **npm**: v8+
- **Git**: Última versão estável
- **Supabase CLI**: Última versão
- **Docker**: Para desenvolvimento local com Supabase

## 🌐 Configuração do Ambiente Local

### Instalação Básica

```bash
# Clonar repositório
git clone https://github.com/seu-usuario/locate-family-connect.git
cd locate-family-connect

# Instalar dependências
npm install

# Copiar arquivo de ambiente
cp .env.example .env.local
```

### Iniciar Supabase Localmente

```bash
# Instalar Supabase CLI (caso não tenha)
npm install -g supabase

# Iniciar Supabase
supabase start

# Aplicar migrações
supabase db push
```

## 📋 Variáveis de Ambiente

### Variáveis Essenciais

| Variável | Descrição | Exemplo |
|----------|-----------|---------|
| `VITE_SUPABASE_URL` | URL do projeto Supabase | `https://abcdefg.supabase.co` |
| `VITE_SUPABASE_ANON_KEY` | Chave anônima do Supabase | `eyJhbGciOiJIUzI1...` |
| `VITE_MAPBOX_TOKEN` | Token da API do MapBox | `pk.eyJ1Ijoic2V1...` |
| `RESEND_API_KEY` | Chave da API Resend | `re_123456...` |

### Configurações Adicionais

```
# .env.local
# Configurações gerais
VITE_APP_NAME=Locate-Family-Connect
VITE_APP_ENV=local
VITE_LOG_LEVEL=debug

# Supabase
VITE_SUPABASE_URL=https://seu-projeto.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR...

# MapBox
VITE_MAPBOX_TOKEN=pk.eyJ1IjoibG9jYXRl...

# Email (Resend)
RESEND_API_KEY=re_123456...
RESEND_DOMAIN=sistema-monitore.com.br
RESEND_FROM=<EMAIL>

# Redis (futuro)
REDIS_URL=redis://localhost:6379
```

## 🚨 Protocolo Anti-Erro para Inicialização

Para garantir que o servidor de desenvolvimento sempre inicie na porta 8080 sem conflitos:

```bash
# Verificar e liberar a porta 8080 se estiver em uso
npx kill-port 8080

# Iniciar servidor de desenvolvimento
npm run dev
```

## 🐳 Docker

### Configuração Docker para Desenvolvimento

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .

# Gerar tipos do Supabase
RUN npm run gen:types

EXPOSE 8080

CMD ["npm", "run", "dev"]
```

### Docker Compose para Stack Completa

```yaml
# docker-compose.yml
version: '3.8'

services:
  frontend:
    build: .
    ports:
      - "8080:8080"
    volumes:
      - ./src:/app/src
    env_file:
      - .env.local
    depends_on:
      - supabase

  supabase:
    image: supabase/supabase-dev
    ports:
      - "54321:54321"  # Postgres
      - "54322:54322"  # Studio
    environment:
      POSTGRES_PASSWORD: postgres
```

### Comandos Docker

```bash
# Iniciar ambiente de desenvolvimento
docker-compose up

# Reconstruir após alterações em dependências
docker-compose build --no-cache

# Acessar logs
docker-compose logs -f frontend
```

## 🚀 Deploy do Frontend

### Preparação para Produção

```bash
# Compilar para produção
npm run build

# Testar build localmente
npm run preview
```

### Deploy com GitHub Actions

`.github/workflows/deploy.yml`:

```yaml
name: Deploy Frontend

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Generate Supabase types
        run: npm run gen:types
      - name: Build
        run: npm run build
      - name: Deploy to hosting
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT }}'
          channelId: live
```

## ⚡ Deploy de Edge Functions

### Preparação Local

```bash
# Navegue até o diretório da função
cd supabase/functions/share-location

# Instalar dependências locais (se necessário)
npm install
```

### Deploy via Supabase CLI

```bash
# Login na CLI (primeira vez)
supabase login

# Link com projeto existente
supabase link --project-ref=seu-projeto-ref

# Deploy da função específica
supabase functions deploy share-location --project-ref=seu-projeto-ref

# Verificar status
supabase functions list
```

### Atualizar Secrets de Edge Functions

```bash
# Definir chave da API Resend como secret
supabase secrets set RESEND_API_KEY=re_123456... --project-ref=seu-projeto-ref
supabase secrets set RESEND_FROM=<EMAIL> --project-ref=seu-projeto-ref
```

## 📊 Monitoramento e Logs

### Logs do Supabase

Para monitorar erros e atividades no Supabase:

1. Acesse o dashboard do Supabase
2. Navegue até "Database" > "API Logs"
3. Para Edge Functions: "Edge Functions" > Selecione função > "Logs"

### Logs Frontend

Para habilitar logs avançados:

```js
// src/lib/logger.js
const logLevels = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
};

const currentLevel = import.meta.env.VITE_LOG_LEVEL || 'info';

export const logger = {
  debug: (message, ...args) => {
    if (logLevels[currentLevel] <= logLevels.debug) {
      console.debug(`[DEBUG] ${message}`, ...args);
    }
  },
  info: (message, ...args) => {
    if (logLevels[currentLevel] <= logLevels.info) {
      console.info(`[INFO] ${message}`, ...args);
    }
  },
  warn: (message, ...args) => {
    if (logLevels[currentLevel] <= logLevels.warn) {
      console.warn(`[WARN] ${message}`, ...args);
    }
  },
  error: (message, ...args) => {
    if (logLevels[currentLevel] <= logLevels.error) {
      console.error(`[ERROR] ${message}`, ...args);
    }
  },
};
```

## 🔄 Integração e Entrega Contínua (CI/CD)

### Fluxo de Integração

```mermaid
graph TD
    A[Commit no GitHub] --> B[Executar Testes]
    B --> C{Testes Passaram?}
    C -->|Sim| D[Build]
    C -->|Não| E[Notificar Erro]
    D --> F[Deploy para Ambiente de Staging]
    F --> G[Testes E2E]
    G --> H{Testes Passaram?}
    H -->|Sim| I[Deploy para Produção]
    H -->|Não| J[Rollback]
```

### GitHub Actions Workflow

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm test

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
```

## 🔄 Atualizações e Rollback

### Processo de Atualização Segura

1. **Backup**:
   ```bash
   # Backup do banco de dados
   supabase db dump -f backup-$(date +%Y%m%d).sql
   ```

2. **Deploy Controlado**:
   ```bash
   # Atualizar apenas Edge Functions
   supabase functions deploy share-location
   
   # Atualizar apenas schema do banco
   supabase db push
   ```

3. **Verificação**:
   ```bash
   # Testar endpoints críticos
   curl -X POST https://seu-projeto.supabase.co/functions/v1/share-location \
     -H "Authorization: Bearer $TOKEN" \
     -d '{"test": true}'
   ```

### Procedimento de Rollback

Em caso de falha:

```bash
# Reverter para versão anterior
git checkout HEAD~1

# Reverter migração específica (se aplicável)
supabase migration revert

# Restaurar backup se necessário
supabase db restore -f backup-20250505.sql
```

## 🔄 Passos após Reinicialização

Checklist para garantir que o ambiente esteja corretamente configurado após reinicializações:

1. Verificar status do Supabase local:
   ```bash
   supabase status
   ```

2. Verificar conexão com banco:
   ```bash
   node scripts/test-db-connection.js
   ```

3. Verificar serviço de email:
   ```bash
   node scripts/test-resend.mjs
   ```

4. Iniciar ambiente de desenvolvimento:
   ```bash
   npx kill-port 8080 && npm run dev
   ```

## 📚 Referências

- [Documentação do Supabase](https://supabase.com/docs)
- [Guia de Deploy do Vite](https://vitejs.dev/guide/static-deploy.html)
- [Supabase CLI](https://supabase.com/docs/reference/cli)
- [Docker Documentation](https://docs.docker.com/)
- [GitHub Actions](https://docs.github.com/en/actions)
