import { renderHook } from '@testing-library/react';
import { useMapControlsLayout } from '../useMapControlsLayout';

jest.mock('../use-mobile', () => ({
  useDevice: jest.fn(),
}));

const { useDevice } = require('../use-mobile');

describe('useMapControlsLayout', () => {
  it('returns left position for tablet portrait', () => {
    useDevice.mockReturnValue({ type: 'tablet', orientation: 'portrait' });
    const { result } = renderHook(() => useMapControlsLayout());
    expect(result.current.getControlsPosition().containerClass).toContain('left-4');
    expect(result.current.getLocationControlsPosition().containerClass).toContain('left-4');
  });

  it('returns compact position for mobile', () => {
    useDevice.mockReturnValue({ type: 'mobile', orientation: 'portrait' });
    const { result } = renderHook(() => useMapControlsLayout());
    expect(result.current.getControlsPosition().containerClass).toContain('right-2');
  });

  it('returns default position for desktop', () => {
    useDevice.mockReturnValue({ type: 'desktop', orientation: 'landscape' });
    const { result } = renderHook(() => useMapControlsLayout());
    expect(result.current.getControlsPosition().containerClass).toContain('right-4');
  });
});
