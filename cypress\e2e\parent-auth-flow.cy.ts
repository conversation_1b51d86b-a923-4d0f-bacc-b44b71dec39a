/// <reference types="cypress" />

describe('Parent Authentication and Dashboard Flow', () => {
  const TEST_USER = {
    email: '<EMAIL>',
    password: '<PERSON><PERSON>@97481716',
    name: '<PERSON>'
  };

  beforeEach(() => {
    // Clear local storage and cookies before each test
    cy.clearLocalStorage();
    cy.clearCookies();
    
    // Visit the home page
    cy.visit('/');
  });

  it('should login successfully and redirect to parent dashboard', () => {
    // Attempt login
    cy.get('[data-cy=email-input]').type(TEST_USER.email);
    cy.get('[data-cy=password-input]').type(TEST_USER.password);
    cy.get('[data-cy=sign-in-button]').click();

    // Should redirect to parent dashboard
    cy.url().should('include', '/parent-dashboard');
    
    // Should display user name in header
    cy.get('[data-cy=user-name]').should('contain', TEST_USER.name);
    
    // Should have dashboard elements
    cy.get('[data-cy=dashboard-container]').should('exist');
    cy.get('[data-cy=students-list]').should('exist');
    cy.get('[data-cy=add-student-button]').should('exist');
    cy.get('[data-cy=profile-button]').should('exist');
    cy.get('[data-cy=sign-out-button]').should('exist');
  });

  it('should display student list correctly', () => {
    // Login first
    cy.get('[data-cy=email-input]').type(TEST_USER.email);
    cy.get('[data-cy=password-input]').type(TEST_USER.password);
    cy.get('[data-cy=sign-in-button]').click();

    // Check student list
    cy.get('[data-cy=students-list]').within(() => {
      // If there are students
      cy.get('[data-cy=student-item]').each(($student) => {
        cy.wrap($student).within(() => {
          cy.get('[data-cy=student-name]').should('exist');
          cy.get('[data-cy=student-email]').should('exist');
        });
      });
    });
  });

  it('should be able to sign out', () => {
    // Login first
    cy.get('[data-cy=email-input]').type(TEST_USER.email);
    cy.get('[data-cy=password-input]').type(TEST_USER.password);
    cy.get('[data-cy=sign-in-button]').click();

    // Click sign out
    cy.get('[data-cy=sign-out-button]').click();

    // Should redirect to login page
    cy.url().should('not.include', '/parent-dashboard');
    cy.get('[data-cy=email-input]').should('exist');
  });
}); 