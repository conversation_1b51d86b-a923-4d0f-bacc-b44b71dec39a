/** Basic end-to-end test covering login and location sharing */

describe('Core user flow', () => {
  const user = {
    email: '<EMAIL>',
    password: 'Escola2025!'
  };

  it('logs in and accesses dashboard', () => {
    cy.visit('/login');
    cy.get('[data-cy="email-input"]').type(user.email);
    cy.get('[data-cy="password-input"]').type(user.password);
    cy.get('[data-cy="submit-button"]').click();
    cy.url({ timeout: 30000 }).should('include', '/parent-dashboard');
  });

  it('navigates to share location and opens dialog', () => {
    cy.get('[data-cy="add-student-button"]').should('exist');
    cy.get('[data-cy="share-location-button"]').click();
    cy.get('body').should('contain.text', 'Localização');
  });
});
