# PWA Build and Testing Guide

This guide explains how to build Locate Family Connect as a Progressive Web App and test its offline capabilities.

## Building the PWA

1. Ensure dependencies are installed:
   ```bash
   npm install
   ```
2. Generate the PWA icons (optional):
   ```bash
   node scripts/generate-icons.js
   ```
3. Create a production build which includes the service worker and manifest:
   ```bash
   npm run build
   ```
   The output will be available in the `dist/` folder.

## Running the Build Locally

Use `serve` to preview the production build with the service worker enabled:

```bash
npx serve -s dist
```

The application is available at `http://localhost:3000` by default. The service worker will install on first load and cache static assets.

## Testing Offline Behaviour

1. Open the application in your browser.
2. After it loads, open the DevTools Network panel and select **Offline**.
3. Reload the page. Because of the service worker and cached data you should still see the application shell and any previously cached information.
4. Return the Network panel to **Online** to resume normal behaviour.

## Running Lighthouse

Google Chrome includes a Lighthouse audit that verifies PWA requirements. With the app served from `dist/` run:

1. Open DevTools and navigate to the **Lighthouse** tab.
2. Select the **Progressive Web App** category and run the audit.
3. Review the report for installability, service worker status and offline capabilities.

## Development Mode

During development the service worker is registered automatically when running:

```bash
npm run dev
```

Keep the dev server running on `http://localhost:4000` and use the same DevTools techniques above to simulate offline mode.

