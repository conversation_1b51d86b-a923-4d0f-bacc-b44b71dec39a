import { Tabs, Ta<PERSON>Content, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Student } from "@/types/student";

interface StudentManagementTabsProps {
  students: Student[];
  onStudentSelect: (student: Student) => void;
  selectedStudent: Student | null;
}

export const StudentManagementTabs: React.FC<StudentManagementTabsProps> = ({
  students,
  onStudentSelect,
  selectedStudent,
}) => {
  const activeStudents = students.filter((s) => s.status === "active");
  // Placeholder arrays for future integration
  const pendingInvites: Student[] = [];
  const historyStudents = students.filter((s) => s.status === "inactive");

  const renderStudentList = (studentList: Student[]) => (
    <div className="space-y-2">
      {studentList.length === 0 ? (
        <p className="text-center text-gray-500 py-4">
          Nenhum estudante encontrado
        </p>
      ) : (
        studentList.map((student) => (
          <div
            key={student.id}
            className={`p-3 border rounded-lg cursor-pointer transition-colors ${
              selectedStudent?.id === student.id
                ? "bg-blue-50 border-blue-200"
                : "hover:bg-gray-50"
            }`}
            onClick={() => onStudentSelect(student)}
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">{student.name}</h3>
                <p className="text-sm text-gray-600">
                  {student.schoolName} - {student.grade} {student.class}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Badge
                  variant={
                    student.status === "active" ? "default" : "secondary"
                  }
                >
                  {student.status === "active" ? "Ativo" : "Inativo"}
                </Badge>
                {student.locationSharing && (
                  <Badge variant="outline">Localização Ativa</Badge>
                )}
              </div>
            </div>
          </div>
        ))
      )}
    </div>
  );

  return (
    <Tabs defaultValue="active" className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="active" className="flex items-center gap-2">
          Ativos
          <Badge variant="secondary">{activeStudents.length}</Badge>
        </TabsTrigger>
        <TabsTrigger value="pending" className="flex items-center gap-2">
          Convites Pendentes
          <Badge variant="secondary">{pendingInvites.length}</Badge>
        </TabsTrigger>
        <TabsTrigger value="history" className="flex items-center gap-2">
          Histórico
          <Badge variant="secondary">{historyStudents.length}</Badge>
        </TabsTrigger>
      </TabsList>

      <TabsContent value="active" className="mt-4">
        {renderStudentList(activeStudents)}
      </TabsContent>

      <TabsContent value="pending" className="mt-4">
        {pendingInvites.length > 0 ? (
          renderStudentList(pendingInvites)
        ) : (
          <p className="text-center text-gray-500 py-4">
            Nenhum convite pendente.
          </p>
        )}
      </TabsContent>

      <TabsContent value="history" className="mt-4">
        {historyStudents.length > 0 ? (
          renderStudentList(historyStudents)
        ) : (
          <p className="text-center text-gray-500 py-4">
            Nenhum registro histórico.
          </p>
        )}
      </TabsContent>
    </Tabs>
  );
};
