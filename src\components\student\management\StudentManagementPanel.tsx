import React from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Student, NewStudent } from "@/types/student";
import { GuardianSharing } from "@/types/permissions";
import { useStudentPermissions } from "@/hooks/useStudentPermissions";
import {
  fetchStudentsForGuardian,
  createStudentWithGuardian,
} from "@/lib/services/student/student-management-service";
import { addSharedGuardian } from "@/lib/services/guardian/guardian-sharing-service";
import { StudentManagementTabs } from "./StudentManagementTabs";
import { StudentDetails } from "./StudentDetails";
import { StudentForm } from "./StudentForm";
import { StudentPermissionsPanel } from "./StudentPermissionsPanel";
import { GuardianSharingPanel } from "./GuardianSharingPanel";
import { useToast } from "@/components/ui/use-toast";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON><PERSON>eader,
  Di<PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>rigger,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { PlusIcon, Loader2 } from "lucide-react";

interface StudentManagementPanelProps {
  guardianId: string;
}

export const StudentManagementPanel: React.FC<StudentManagementPanelProps> = ({
  guardianId,
}) => {
  const [selectedStudent, setSelectedStudent] = React.useState<Student | null>(
    null,
  );
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [activeTab, setActiveTab] = React.useState("details");
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Use the new permissions hook when a student is selected
  const {
    student: studentWithPermissions,
    updateStudent,
    updateGuardianSharing,
    isUpdatingSharing,
  } = useStudentPermissions(selectedStudent?.id || "", guardianId);

  const {
    data: students,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["students", guardianId],
    queryFn: () => fetchStudentsForGuardian(guardianId),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 15, // 15 minutes
  });

  // Handle student updates including permissions
  const handleStudentUpdate = async (updates: Partial<Student>) => {
    if (!selectedStudent) return;

    try {
      await updateStudent(updates);
      toast({
        title: "Sucesso",
        description: "Informações do aluno atualizadas com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao atualizar informações do aluno.",
        variant: "destructive",
      });
    }
  };

  // Handle guardian sharing updates
  const handleSharingUpdate = async (updates: Partial<GuardianSharing>) => {
    if (!selectedStudent) return;

    try {
      await updateGuardianSharing(updates);
      toast({
        title: "Sucesso",
        description:
          "Configurações de compartilhamento atualizadas com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao atualizar configurações de compartilhamento.",
        variant: "destructive",
      });
    }
  };

  // Handle adding a new student
  const handleStudentAdd = async (newStudent: NewStudent) => {
    try {
      await createStudentWithGuardian({ ...newStudent, guardianId });
      setIsDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ["students", guardianId] });
      toast({
        title: "Sucesso",
        description: "Estudante adicionado com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao adicionar estudante.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Carregando estudantes...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-md border border-red-200">
        <h3 className="text-red-800 font-medium">
          Erro ao carregar estudantes
        </h3>
        <p className="text-red-600">
          {(error as any)?.message || "Erro desconhecido"}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Gerenciamento de Estudantes</h2>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusIcon className="mr-2 h-4 w-4" />
              Adicionar Estudante
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Adicionar Novo Estudante</DialogTitle>
            </DialogHeader>
            <StudentForm onSubmit={handleStudentAdd} />
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-12 gap-4">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Estudantes</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : (
              <StudentManagementTabs
                students={students || []}
                onStudentSelect={setSelectedStudent}
                selectedStudent={selectedStudent}
              />
            )}
          </CardContent>
        </Card>

        {selectedStudent && (
          <div className="col-span-8">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="details">Detalhes</TabsTrigger>
                <TabsTrigger value="permissions">Permissões</TabsTrigger>
                <TabsTrigger value="sharing">Compartilhamento</TabsTrigger>
              </TabsList>

              <TabsContent value="details">
                <StudentDetails
                  student={studentWithPermissions || selectedStudent}
                  onUpdate={handleStudentUpdate}
                />
              </TabsContent>

              <TabsContent value="permissions">
                <StudentPermissionsPanel
                  studentId={selectedStudent.id}
                  guardianId={guardianId}
                />
              </TabsContent>

              <TabsContent value="sharing">
                <GuardianSharingPanel
                  student={studentWithPermissions || selectedStudent}
                  onSharingUpdate={handleSharingUpdate}
                  onAddGuardian={async ({ email }) => {
                    // Use the simplified addSharedGuardian function with read access
                    try {
                      await addSharedGuardian(selectedStudent.id, {
                        email,
                        access_level: "read",
                      });
                      toast({
                        title: "Sucesso",
                        description: "Responsável adicionado com sucesso.",
                      });
                    } catch (error) {
                      toast({
                        title: "Erro",
                        description: "Erro ao adicionar responsável.",
                        variant: "destructive",
                      });
                    }
                  }}
                  onRemoveGuardian={async (guardianId) => {
                    if (!selectedStudent?.permissions?.guardianSharing) return;

                    try {
                      await updateGuardianSharing({
                        sharedWith:
                          selectedStudent.permissions.guardianSharing.sharedWith.filter(
                            (g) => g.guardianId !== guardianId,
                          ),
                      });

                      toast({
                        title: "Sucesso",
                        description: "Responsável removido com sucesso.",
                      });
                    } catch (error) {
                      toast({
                        title: "Erro",
                        description: "Erro ao remover responsável.",
                        variant: "destructive",
                      });
                    }
                  }}
                  isUpdating={isUpdatingSharing}
                />
              </TabsContent>
            </Tabs>
          </div>
        )}
      </div>
    </div>
  );
};
