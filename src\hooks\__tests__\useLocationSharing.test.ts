import { renderHook, act } from '@testing-library/react';
import { useLocationSharing } from '../useLocationSharing';

jest.mock('../use-toast', () => ({ useToast: () => ({ toast: jest.fn() }) }));

jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ error: null }),
    functions: { invoke: jest.fn().mockResolvedValue({ error: null }) },
  }
}));

const { supabase } = require('@/lib/supabase');

const mockGeolocation = {
  getCurrentPosition: (success: any) => {
    success({ coords: { latitude: 1, longitude: 2 } });
  }
};

// @ts-ignore
global.navigator.geolocation = mockGeolocation;

describe('useLocationSharing', () => {
  it('shares location successfully', async () => {
    const { result } = renderHook(() => useLocationSharing());
    let response: boolean | undefined;
    await act(async () => {
      response = await result.current.shareLocation('<EMAIL>', 'Student');
    });
    expect(response).toBe(true);
    expect(supabase.from).toHaveBeenCalled();
    expect(supabase.functions.invoke).toHaveBeenCalled();
  });

  it('handles errors gracefully', async () => {
    supabase.single.mockResolvedValueOnce({ error: new Error('fail') });
    const { result } = renderHook(() => useLocationSharing());
    let response: boolean | undefined;
    await act(async () => {
      response = await result.current.shareLocation('<EMAIL>', 'Student');
    });
    expect(response).toBe(false);
  });
});
