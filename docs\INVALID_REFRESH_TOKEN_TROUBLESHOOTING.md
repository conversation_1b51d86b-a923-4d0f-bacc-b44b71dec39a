# Invalid Refresh Token Troubleshooting

This guide explains how to resolve the **"Invalid Refresh Token: Refresh Token Not Found"** error when using Supabase authentication.

## Why It Happens
- The client cannot find a valid refresh token in local storage or AsyncStorage.
- It often occurs if the session was cleared, expired, or if multiple Supabase clients overwrite each other's tokens.

## Quick Fix Steps
1. **Sign out and sign in again** to generate a new session.
2. **Clear localStorage / AsyncStorage** to remove stale tokens.
3. **Ensure a single Supabase client instance** is used across the app.
4. **Restart the development server** after clearing storage.

Following these steps usually restores session refresh functionality.
