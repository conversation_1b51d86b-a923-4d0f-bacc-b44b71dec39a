import { supabase } from '@/lib/supabase';
import { Student, NewStudent } from '@/types/student';
import { Database } from '@/integrations/supabase/types';

// Tipos do banco de dados
type Profile = Database['public']['Tables']['profiles']['Row'];

/**
 * Serviço de gerenciamento de estudantes que usa os tipos corretos do Supabase
 * e implementa as operações CRUD de forma segura
 */
export class StudentManagementService {
  /**
   * Busca todos os estudantes vinculados a um responsável
   * @param guardianId ID do responsável
   */
  static async getStudentsForGuardian(guardianId: string): Promise<Student[]> {
    try {
      // Busca registros de guardians (responsáveis) relacionados a estudantes
      const { data: guardianRecords, error: guardianError } = await supabase
        .from('guardians')
        .select('student_id, status, is_active')
        .eq('id', guardianId)
        .eq('is_active', true);

      if (guardianError) {
        console.error('Erro ao buscar relações estudante/responsável:', guardianError);
        throw guardianError;
      }

      if (!guardianRecords || guardianRecords.length === 0) {
        return [];
      }

      // Extraímos os IDs dos estudantes
      const studentIds = guardianRecords
        .filter(rec => rec.student_id)
        .map(rec => typeof rec.student_id === 'string' ? rec.student_id : String(rec.student_id)); // Ensure string[]

      if (studentIds.length === 0) {
        return [];
      }

      // Buscamos os perfis dos estudantes
      const { data: profiles, error: profileError } = await supabase
        .from('profiles')
        .select(`
          id, 
          full_name,
          email, 
          phone,
          cpf,
          birth_date,
          status, 
          created_at, 
          updated_at, 
          user_type
        `)
        .in('id', studentIds.map(id => Number(id)))
        .eq('user_type', 'student');

      if (profileError) {
        console.error('Erro ao buscar perfis dos estudantes:', profileError);
        throw profileError;
      }

      if (!profiles || profiles.length === 0) {
        return [];
      }

      // Para cada estudante, buscamos a última localização
      const studentsWithData = await Promise.all(
        profiles.map(async (profile) => {
          // Preencher campos obrigatórios ausentes para o tipo Profile
          const completeProfile = {
            last_login_at: (profile as any).last_login_at ?? null,
            login_count: (profile as any).login_count ?? null,
            parent_cpf: (profile as any).parent_cpf ?? null,
            parent_email: (profile as any).parent_email ?? null,
            registration_status: (profile as any).registration_status ?? null,
            requires_parent_confirmation: (profile as any).requires_parent_confirmation ?? null,
            user_id: (profile as any).user_id ?? null,
            // Only set phone if not present in ...profile
            ...profile
          };
          // Buscar a última localização do estudante
          const { data: locationData } = await supabase
            .from('locations')
            .select('latitude, longitude, timestamp, shared_with_guardians')
            .eq('user_id', String(profile.id))
            .order('timestamp', { ascending: false })
            .limit(1)
            .maybeSingle();

          // Mapeia para o formato esperado pelo frontend
          const guardianRelationship = guardianRecords.find(
            rel => String(rel.student_id) === String(profile.id)
          );
          return this._mapToStudentModel(
            completeProfile, 
            locationData || null, 
            guardianId, 
            guardianRelationship?.status || 'pending'
          );
        })
      );

      return studentsWithData;
    } catch (error) {
      console.error('Erro ao buscar estudantes:', error);
      return [];
    }
  }

  /**
   * Cria um novo estudante e o vincula ao responsável
   * @param studentData Dados do novo estudante
   */
  static async createStudent(studentData: NewStudent): Promise<Student | null> {
    try {
      // 1. Gerar email do estudante baseado no nome (slug)
      const studentEmail = `${studentData.name.toLowerCase().replace(/\s+/g, '.')}@estudante.sistema-monitore.com.br`;
      
      // 2. Verificar se já existe um perfil com esse email
      const { data: existingProfile, error: searchError } = await supabase
        .from('profiles')
        .select('id')
        .eq('email', studentEmail)
        .maybeSingle();

      if (searchError) {
        console.error('Erro ao verificar estudante existente:', searchError);
        throw searchError;
      }

      // 3. Definir o ID do estudante (novo ou existente)
      let studentId = existingProfile?.id;

      // 4. Se não existe, criar um novo perfil
      if (!studentId) {
        const { data: newProfile, error: createError } = await supabase
          .from('profiles')
          .insert({
            full_name: studentData.name,
            email: studentEmail,
            user_type: 'student',
            status: 'active',
            cpf: '', // placeholder
            birth_date: null, // placeholder
            phone: null, // placeholder
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          .select('id')
          .single();

        if (createError) {
          console.error('Erro ao criar perfil do estudante:', createError);
          throw createError;
        }

        studentId = newProfile.id;
      }

      // 5. Verificar se já existe uma relação guardian-student
      const { data: existingRelation } = await supabase
        .from('guardians')
        .select('id')
        .eq('id', String(studentData.guardianId))
        .eq('student_id', String(studentId))
        .maybeSingle();

      // 6. Criar ou atualizar a relação
      if (existingRelation) {
        // Atualizar relação existente
        await supabase
          .from('guardians')
          .update({
            status: 'active',
            is_active: true
          })
          .eq('id', String(studentData.guardianId))
          .eq('student_id', String(studentId));
      } else {
        // Buscar informações do responsável
        const { data: guardianProfile } = await supabase
          .from('profiles')
          .select('full_name, email')
          .eq('id', Number(studentData.guardianId))
          .single();

        // Criar nova relação
        const { error: relationError } = await supabase
          .from('guardians')
          .insert({
            id: String(studentData.guardianId),
            student_id: String(studentId),
            status: 'active',
            is_active: true,
            full_name: guardianProfile?.full_name || 'Responsável',
            email: guardianProfile?.email || '<EMAIL>'
          });

        if (relationError) {
          console.error('Erro ao criar relação responsável-estudante:', relationError);
          throw relationError;
        }
      }

      // 7. Buscar o estudante completo
      return this.getStudentById(String(studentId), String(studentData.guardianId));

    } catch (error) {
      console.error('Erro ao criar estudante:', error);
      throw error;
    }
  }

  /**
   * Atualiza os dados de um estudante
   * @param studentId ID do estudante
   * @param updates Dados a serem atualizados
   * @param guardianId ID do responsável que está fazendo a atualização
   */
  static async updateStudent(
    studentId: string, 
    updates: Partial<Student>, 
    guardianId: string
  ): Promise<Student | null> {
    try {
      // 1. Verificar permissão
      const { data: relation } = await supabase
        .from('guardians')
        .select('id')
        .eq('id', guardianId)
        .eq('student_id', studentId)
        .eq('is_active', true)
        .maybeSingle();

      if (!relation) {
        throw new Error('Você não tem permissão para atualizar este estudante');
      }

      // 2. Atualizar apenas os campos seguros no perfil
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          full_name: updates.name ?? undefined
        })
        .eq('id', Number(studentId)); // Convert studentId to number

      if (updateError) {
        console.error('Erro ao atualizar estudante:', updateError);
        throw updateError;
      }

      // 3. Se houver configuração de compartilhamento de localização, atualizar
      if (updates.locationSharing !== undefined) {
        // Esta é uma implementação simplificada
        console.log('Atualização de compartilhamento:', updates.locationSharing);
        
        // Aqui pode ser necessário atualizar alguma configuração
        // de compartilhamento, dependendo da implementação
      }

      // 4. Retornar o estudante atualizado
      return this.getStudentById(String(studentId), guardianId);

    } catch (error) {
      console.error('Erro ao atualizar estudante:', error);
      return null;
    }
  }

  /**
   * Remove um estudante da lista de um responsável
   * @param studentId ID do estudante
   * @param guardianId ID do responsável
   */
  static async removeStudentRelationship(studentId: string, guardianId: string): Promise<boolean> {
    try {
      // Marcamos como inativo em vez de excluir
      const { error } = await supabase
        .from('guardians')
        .update({
          status: 'inactive',
          is_active: false
        })
        .eq('id', guardianId)
        .eq('student_id', studentId);

      if (error) {
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Erro ao remover relação estudante-responsável:', error);
      return false;
    }
  }

  /**
   * Busca um estudante específico pelo ID
   * @param studentId ID do estudante
   * @param guardianId ID do responsável
   */
  static async getStudentById(studentId: string, guardianId: string): Promise<Student | null> {
    try {
      // 1. Verificar permissão
      const { data: relation, error: relationError } = await supabase
        .from('guardians')
        .select('status')
        .eq('id', guardianId)
        .eq('student_id', studentId)
        .maybeSingle();

      if (relationError || !relation) {
        throw new Error('Permissão negada para acessar este estudante');
      }

      // 2. Buscar perfil do estudante
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', Number(studentId))
        .single();

      if (profileError || !profile) {
        throw new Error('Estudante não encontrado');
      }

      // 3. Buscar a última localização
      const { data: location } = await supabase
        .from('locations')
        .select('latitude, longitude, timestamp, shared_with_guardians')
        .eq('user_id', studentId)
        .order('timestamp', { ascending: false })
        .limit(1)
        .maybeSingle();

      // 4. Mapear para o formato de frontend
      return this._mapToStudentModel(profile, location, guardianId, relation.status || 'pending');

    } catch (error) {
      console.error('Erro ao buscar estudante:', error);
      return null;
    }
  }

  /**
   * Mapeia dados do banco para o modelo do frontend
   */
  private static _mapToStudentModel(
    profile: Profile,
    location: any | null,
    guardianId: string,
    status: string
  ): Student {
    return {
      id: String(profile.id),
      user_id: String(profile.id),
      name: profile.full_name || '',
      schoolId: '',
      schoolName: '',
      grade: '',
      class: '',
      status: (status === 'active' || status === 'inactive') ? status : 'inactive',
      guardianId: String(guardianId),
      locationSharing: !!location?.shared_with_guardians,
      lastLocation: location && typeof location.latitude === 'number' && typeof location.longitude === 'number' && location.timestamp ? {
        latitude: location.latitude,
        longitude: location.longitude,
        timestamp: location.timestamp,
      } : null,
      createdAt: profile.created_at || '',
      updatedAt: profile.updated_at || profile.created_at || '',
    };
  }
}

// Funções exportadas para compatibilidade com a API existente
export const fetchStudentsForGuardian = StudentManagementService.getStudentsForGuardian.bind(StudentManagementService);
export const createStudentWithGuardian = StudentManagementService.createStudent.bind(StudentManagementService);
export const updateStudentProfile = StudentManagementService.updateStudent.bind(StudentManagementService);
export const removeStudentGuardianRelationship = StudentManagementService.removeStudentRelationship.bind(StudentManagementService);
export const fetchStudentById = StudentManagementService.getStudentById.bind(StudentManagementService);
