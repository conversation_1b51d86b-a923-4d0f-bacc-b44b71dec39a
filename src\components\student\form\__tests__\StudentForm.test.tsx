import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { StudentForm } from '../StudentForm';

const mockHandleCreateStudent = jest.fn();
const mockToast = jest.fn();

jest.mock('../../hooks/useCreateStudent', () => ({
  useCreateStudent: () => ({ loading: false, handleCreateStudent: mockHandleCreateStudent })
}));

jest.mock('@/contexts/UnifiedAuthContext', () => ({
  useUser: () => ({ user: { id: 'guardian1' } })
}));

jest.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({ toast: mockToast })
}));

describe('StudentForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('creates student successfully', async () => {
    mockHandleCreateStudent.mockResolvedValue({ success: true });
    const onSuccess = jest.fn();
    render(<StudentForm onSuccess={onSuccess} />);

    fireEvent.change(screen.getByLabelText('Nome'), { target: { value: 'John Doe' } });
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByLabelText('Escola'), { target: { value: 'School' } });
    fireEvent.change(screen.getByLabelText('Turma'), { target: { value: 'A' } });
    // Select grade
    fireEvent.mouseDown(screen.getByText('Selecione a série'));
    fireEvent.click(screen.getByText('1º Ano'));

    fireEvent.click(screen.getByRole('button', { name: /adicionar estudante/i }));

    await waitFor(() => {
      expect(mockHandleCreateStudent).toHaveBeenCalledWith({
        name: 'John Doe',
        schoolId: '',
        schoolName: 'School',
        grade: '1',
        class: 'A',
        guardianId: 'guardian1',
      });
    });

    expect(onSuccess).toHaveBeenCalled();
    expect(mockToast).not.toHaveBeenCalledWith(expect.objectContaining({ variant: 'destructive' }));
  });

  it('shows error on failure', async () => {
    mockHandleCreateStudent.mockRejectedValue(new Error('fail'));
    const onSuccess = jest.fn();
    render(<StudentForm onSuccess={onSuccess} />);

    fireEvent.change(screen.getByLabelText('Nome'), { target: { value: 'John Doe' } });
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByLabelText('Escola'), { target: { value: 'School' } });
    fireEvent.change(screen.getByLabelText('Turma'), { target: { value: 'A' } });
    fireEvent.mouseDown(screen.getByText('Selecione a série'));
    fireEvent.click(screen.getByText('1º Ano'));

    fireEvent.click(screen.getByRole('button', { name: /adicionar estudante/i }));

    await waitFor(() => {
      expect(mockHandleCreateStudent).toHaveBeenCalled();
    });

    expect(onSuccess).not.toHaveBeenCalled();
    expect(mockToast).toHaveBeenCalledWith(expect.objectContaining({ variant: 'destructive' }));
  });
});
