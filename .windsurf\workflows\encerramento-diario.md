---
description: Protocolo de Encerramento Diário de Desenvolvimento
---

# Protocolo de Encerramento Diário

Este workflow automatiza o encerramento seguro de sessões de desenvolvimento do projeto Locate-Family-Connect.

## Passos do Protocolo

### 1. Encerrar Serviços Docker
```bash
# Parar todos os contêineres relacionados ao projeto
docker compose -f docker-compose.yml down

# Verificar que nenhum contêiner permanece em execução
docker ps -a
```

### 2. Backup de Arquivos Sensíveis
```bash
# Executar script de backup automatizado
node scripts/encerramento-diario.mjs
```

### 3. Verificação de Segurança
```bash
# Verificar estado do git e arquivos não rastreados
git status
```

### 4. Documentar Estado e Planejar Próxima Sessão
```bash
# Abrir arquivo de plano para edição
code docs/plano-execucao.md
```

### 5. Executar Diagnóstico Final
```bash
# Verificar integridade do Docker
docker system df

# Verificar se há alterações não salvas
git status
```

## Retomada no Próximo Dia

Para iniciar uma nova sessão:

```bash
# Restaurar ambiente de desenvolvimento
node scripts/iniciar-ambiente.mjs
```
