---
description: DEVELOPMENT_GUIDELINES
---

Anti-error Verification Protocols
To minimize the risk of errors, implement multiple layers of verification. Use automated tests, code reviews, and continuous integration to catch issues early. Establish peer review and pair programming practices for critical code changes. Ensure changes are validated against requirements and regression tests.

Automated Testing: Maintain unit, integration, and end-to-end tests that run on each commit.
Code Reviews: All changes must be reviewed by at least one other developer.
Continuous Integration (CI): Set up automated builds and test runs on every pull request.
Static Analysis: Use linters and type checkers to enforce code quality and catch errors early.
Documentation Verification: Keep documentation up to date and review it as part of the change process.
Git Automation Rules
Define clear rules for using Git and automated pipelines to streamline development. Adopt a consistent branching strategy and enforce commit message conventions. Automate repetitive tasks like merging, tagging, and deployments through scripts or CI/CD workflows.

Branching Model: Use a well-defined branching strategy (e.g., Gitflow, GitHub Flow).
Commit Message Conventions: Include ticket IDs, scope, and concise descriptions in commits.
Pull Requests (PRs): Require PRs for all changes. Use templates to ensure consistent information.
Automated Workflows: Use CI/CD tools to automate testing, linting, and deployments on push events.
Pre-commit Hooks: Configure hooks (e.g., linting, format checks) to run locally before commits.
Project Architecture and Stack Overview
Provide a high-level description of the system architecture and technology stack. Clearly outline the responsibilities of each layer and the tools used. This helps all developers understand how components interact and what technologies to use.

Frontend: (e.g., React, TypeScript) for UI development; state management (Redux, MobX) and styling frameworks (CSS Modules, Tailwind).
Backend/API: (e.g., Node.js, Express, GraphQL) for business logic, data processing, and API endpoints.
Database: (e.g., PostgreSQL, MongoDB) for data storage; use an ORM/ODM (like Sequelize or TypeORM) if applicable.
Infrastructure: Cloud platform (AWS, Azure, GCP) and containerization (Docker, Kubernetes); consider serverless options for microservices.
Communication: REST or GraphQL APIs, WebSockets, or pub/sub for real-time interactions.
Security Layer: Authentication mechanisms (OAuth 2.0, JWT), authorization roles, and encryption protocols (TLS).
File Structure Overview
Maintain a consistent file and directory structure across the project. Group related files and modules logically. Include a root-level README and ensure each major directory has an overview if needed.

Root Directory: Contains main configuration files (e.g., package.json), and documentation.
src/ Folder: Primary source code directory.
components/: Reusable UI components.
pages/ or views/: Top-level pages or route handlers.
hooks/: Custom React hooks or shared logic hooks.
services/ or api/: Modules handling API calls and business logic.
utils/: Utility functions and shared helpers.
assets/: Static assets (images, fonts, etc.).
styles/: Global stylesheets or theme files.
tests/ Folder: Contains unit and integration test suites.
Configuration Files: .env folder for environment-specific settings and secrets (do not commit secrets).
Structure and Organization Best Practices
Encourage modular design and clear separation of concerns. Follow naming conventions and keep consistent code patterns. This makes the codebase easier to navigate and maintain.

Single Responsibility: Each module or component should have a single well-defined purpose.
Naming Conventions: Use consistent naming (e.g., for functions and variables).
Modularity: Group code by feature or domain to avoid large monolithic files.
Separation of Concerns: Keep business logic separate from presentation logic.
Documentation: Document public functions and APIs. Maintain an updated README and comment complex logic.
Dependency Management: Keep dependencies updated. Remove unused packages.
Code Style: Enforce consistent formatting with tools (Prettier, ESLint) and follow language-specific best practices.
Configuration and Environment Instructions
Specify how to configure the application and manage different environments (development, staging, production). Provide details on environment variables, configuration files, and secret management.

Environment Variables: Store secrets and config in commit-sensitive information to version control.
Configuration Files: Use separate config files (e.g., .env) or environment-based logic within code.
Setup Instructions: Document steps to set up the project locally (install dependencies, set environment variables, run database migrations, etc.).
Environment Parity: Keep development, staging, and production environments as similar as possible.
Example: Provide a sample .env.example with required variables.
Secret Management: Use vaults or encrypted storage for production secrets (e.g., AWS Secrets Manager, HashiCorp Vault).
Consistency and Reuse Strategies
Promote consistency across the codebase and maximize code reuse. Use shared libraries or components for common functionality. Enforce standards through tooling and guidelines.

Component Libraries: Centralize reusable UI elements and styles.
Shared Utilities: Keep common helper functions in a shared utils or lib directory.
Coding Standards: Use linting rules and code formatters consistently across the team.
Documentation: Maintain style guides or wikis for patterns and best practices.
DRY Principle: Avoid duplication by abstracting repeated logic into functions or components.
Versioning: Use semantic versioning for shared modules and document changes.
Debugging Protocols
Establish a systematic approach to identify and fix issues. Utilize debugging tools and logging to trace problems, and document common troubleshooting 