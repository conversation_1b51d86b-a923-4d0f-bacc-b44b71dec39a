export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      account_deletion_requests: {
        Row: {
          created_at: string
          guardian_notes: string | null
          id: string
          processed_at: string | null
          processed_by_guardian_email: string | null
          reason: string | null
          requested_at: string
          status: string
          student_email: string
          student_id: string
          student_name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          guardian_notes?: string | null
          id?: string
          processed_at?: string | null
          processed_by_guardian_email?: string | null
          reason?: string | null
          requested_at?: string
          status?: string
          student_email: string
          student_id: string
          student_name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          guardian_notes?: string | null
          id?: string
          processed_at?: string | null
          processed_by_guardian_email?: string | null
          reason?: string | null
          requested_at?: string
          status?: string
          student_email?: string
          student_id?: string
          student_name?: string
          updated_at?: string
        }
        Relationships: []
      }
      auth_logs: {
        Row: {
          event_type: string | null
          id: number
          metadata: Json | null
          occurred_at: string | null
          user_id: string | null
        }
        Insert: {
          event_type?: string | null
          id?: number
          metadata?: Json | null
          occurred_at?: string | null
          user_id?: string | null
        }
        Update: {
          event_type?: string | null
          id?: number
          metadata?: Json | null
          occurred_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      guardian_removal_requests: {
        Row: {
          created_at: string
          guardian_email: string
          guardian_id: string
          guardian_notes: string | null
          id: string
          processed_at: string | null
          processed_by_guardian_email: string | null
          reason: string | null
          requested_at: string
          status: string
          student_id: string
          student_name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          guardian_email: string
          guardian_id: string
          guardian_notes?: string | null
          id?: string
          processed_at?: string | null
          processed_by_guardian_email?: string | null
          reason?: string | null
          requested_at?: string
          status?: string
          student_id: string
          student_name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          guardian_email?: string
          guardian_id?: string
          guardian_notes?: string | null
          id?: string
          processed_at?: string | null
          processed_by_guardian_email?: string | null
          reason?: string | null
          requested_at?: string
          status?: string
          student_id?: string
          student_name?: string
          updated_at?: string
        }
        Relationships: []
      }
      guardians: {
        Row: {
          birth_date: string | null
          cpf: string | null
          created_at: string
          email: string
          full_name: string
          id: string
          is_active: boolean | null
          phone: string | null
          status: string | null
          student_id: string | null
          updated_at: string | null
        }
        Insert: {
          birth_date?: string | null
          cpf?: string | null
          created_at?: string
          email: string
          full_name: string
          id?: string
          is_active?: boolean | null
          phone?: string | null
          status?: string | null
          student_id?: string | null
          updated_at?: string | null
        }
        Update: {
          birth_date?: string | null
          cpf?: string | null
          created_at?: string
          email?: string
          full_name?: string
          id?: string
          is_active?: boolean | null
          phone?: string | null
          status?: string | null
          student_id?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      location_history: {
        Row: {
          accuracy: number | null
          battery_level: number | null
          bearing: number | null
          created_at: string | null
          id: string
          is_mocked: boolean | null
          latitude: number
          location_id: string | null
          longitude: number
          source: string | null
          speed: number | null
          user_id: string | null
        }
        Insert: {
          accuracy?: number | null
          battery_level?: number | null
          bearing?: number | null
          created_at?: string | null
          id?: string
          is_mocked?: boolean | null
          latitude: number
          location_id?: string | null
          longitude: number
          source?: string | null
          speed?: number | null
          user_id?: string | null
        }
        Update: {
          accuracy?: number | null
          battery_level?: number | null
          bearing?: number | null
          created_at?: string | null
          id?: string
          is_mocked?: boolean | null
          latitude?: number
          location_id?: string | null
          longitude?: number
          source?: string | null
          speed?: number | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "location_history_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
        ]
      }
      location_notifications: {
        Row: {
          created_at: string | null
          guardian_email: string
          guardian_id: string | null
          id: string
          location_id: string | null
          status: string
          student_id: string
          viewed_at: string | null
        }
        Insert: {
          created_at?: string | null
          guardian_email: string
          guardian_id?: string | null
          id?: string
          location_id?: string | null
          status?: string
          student_id: string
          viewed_at?: string | null
        }
        Update: {
          created_at?: string | null
          guardian_email?: string
          guardian_id?: string | null
          id?: string
          location_id?: string | null
          status?: string
          student_id?: string
          viewed_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "location_notifications_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
        ]
      }
      locations: {
        Row: {
          accuracy: number | null
          address: string | null
          battery_level: number | null
          bearing: number | null
          created_at: string | null
          id: string
          is_mocked: boolean | null
          latitude: number
          longitude: number
          shared_with_guardians: boolean | null
          source: string | null
          speed: number | null
          timestamp: string
          user_id: string
        }
        Insert: {
          accuracy?: number | null
          address?: string | null
          battery_level?: number | null
          bearing?: number | null
          created_at?: string | null
          id?: string
          is_mocked?: boolean | null
          latitude: number
          longitude: number
          shared_with_guardians?: boolean | null
          source?: string | null
          speed?: number | null
          timestamp?: string
          user_id?: string
        }
        Update: {
          accuracy?: number | null
          address?: string | null
          battery_level?: number | null
          bearing?: number | null
          created_at?: string | null
          id?: string
          is_mocked?: boolean | null
          latitude?: number
          longitude?: number
          shared_with_guardians?: boolean | null
          source?: string | null
          speed?: number | null
          timestamp?: string
          user_id?: string
        }
        Relationships: []
      }
      notification_logs: {
        Row: {
          content: string | null
          delivered_at: string | null
          error_message: string | null
          id: string
          message_type: string
          metadata: Json | null
          method: string
          recipient: string
          recipient_id: string | null
          sent_at: string | null
          status: string | null
          user_id: string
        }
        Insert: {
          content?: string | null
          delivered_at?: string | null
          error_message?: string | null
          id?: string
          message_type: string
          metadata?: Json | null
          method: string
          recipient: string
          recipient_id?: string | null
          sent_at?: string | null
          status?: string | null
          user_id: string
        }
        Update: {
          content?: string | null
          delivered_at?: string | null
          error_message?: string | null
          id?: string
          message_type?: string
          metadata?: Json | null
          method?: string
          recipient?: string
          recipient_id?: string | null
          sent_at?: string | null
          status?: string | null
          user_id?: string
        }
        Relationships: []
      }
      parent_confirmation_invites: {
        Row: {
          confirmed_at: string | null
          confirmed_by_parent_id: string | null
          created_at: string
          expires_at: string
          id: string
          parent_cpf: string
          parent_email: string
          status: string
          student_birth_date: string
          student_email: string
          student_id: string
          student_name: string
          token: string
        }
        Insert: {
          confirmed_at?: string | null
          confirmed_by_parent_id?: string | null
          created_at?: string
          expires_at?: string
          id?: string
          parent_cpf: string
          parent_email: string
          status?: string
          student_birth_date: string
          student_email: string
          student_id: string
          student_name: string
          token: string
        }
        Update: {
          confirmed_at?: string | null
          confirmed_by_parent_id?: string | null
          created_at?: string
          expires_at?: string
          id?: string
          parent_cpf?: string
          parent_email?: string
          status?: string
          student_birth_date?: string
          student_email?: string
          student_id?: string
          student_name?: string
          token?: string
        }
        Relationships: []
      }
      pending_shares: {
        Row: {
          created_at: string | null
          expires_at: string | null
          guardian_email: string
          id: string
          status: string | null
          student_id: string
        }
        Insert: {
          created_at?: string | null
          expires_at?: string | null
          guardian_email: string
          id?: string
          status?: string | null
          student_id: string
        }
        Update: {
          created_at?: string | null
          expires_at?: string | null
          guardian_email?: string
          id?: string
          status?: string | null
          student_id?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          birth_date: string | null
          cpf: string
          created_at: string | null
          email: string
          full_name: string
          id: number
          last_login_at: string | null
          login_count: number | null
          parent_cpf: string | null
          parent_email: string | null
          phone: string | null
          registration_status: string | null
          requires_parent_confirmation: boolean | null
          status: string | null
          updated_at: string | null
          user_id: string | null
          user_type: string
        }
        Insert: {
          birth_date?: string | null
          cpf: string
          created_at?: string | null
          email?: string
          full_name: string
          id?: number
          last_login_at?: string | null
          login_count?: number | null
          parent_cpf?: string | null
          parent_email?: string | null
          phone?: string | null
          registration_status?: string | null
          requires_parent_confirmation?: boolean | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
          user_type: string
        }
        Update: {
          birth_date?: string | null
          cpf?: string
          created_at?: string | null
          email?: string
          full_name?: string
          id?: number
          last_login_at?: string | null
          login_count?: number | null
          parent_cpf?: string | null
          parent_email?: string | null
          phone?: string | null
          registration_status?: string | null
          requires_parent_confirmation?: boolean | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
          user_type?: string
        }
        Relationships: []
      }
      student_guardian_relationships: {
        Row: {
          created_at: string | null
          guardian_id: string
          id: string
          is_primary: boolean | null
          relationship_type: string
          student_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          guardian_id: string
          id?: string
          is_primary?: boolean | null
          relationship_type: string
          student_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          guardian_id?: string
          id?: string
          is_primary?: boolean | null
          relationship_type?: string
          student_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      student_location_history: {
        Row: {
          accuracy: number | null
          battery_level: number | null
          created_at: string | null
          id: string
          latitude: number
          longitude: number
          student_id: string
          timestamp: string | null
        }
        Insert: {
          accuracy?: number | null
          battery_level?: number | null
          created_at?: string | null
          id?: string
          latitude: number
          longitude: number
          student_id: string
          timestamp?: string | null
        }
        Update: {
          accuracy?: number | null
          battery_level?: number | null
          created_at?: string | null
          id?: string
          latitude?: number
          longitude?: number
          student_id?: string
          timestamp?: string | null
        }
        Relationships: []
      }
      students: {
        Row: {
          class: string
          created_at: string | null
          grade: string
          guardian_id: string
          id: string
          last_location: Json | null
          location_sharing: boolean
          name: string
          school_id: string
          school_name: string
          status: string
          updated_at: string | null
        }
        Insert: {
          class: string
          created_at?: string | null
          grade: string
          guardian_id: string
          id?: string
          last_location?: Json | null
          location_sharing?: boolean
          name: string
          school_id: string
          school_name: string
          status?: string
          updated_at?: string | null
        }
        Update: {
          class?: string
          created_at?: string | null
          grade?: string
          guardian_id?: string
          id?: string
          last_location?: Json | null
          location_sharing?: boolean
          name?: string
          school_id?: string
          school_name?: string
          status?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      communication_preferences: {
        Row: {
          id: string
          user_id: string
          email_notifications: boolean | null
          sms_notifications: boolean | null
          whatsapp_notifications: boolean | null
          location_sharing_enabled: boolean | null
          emergency_contact_email: string | null
          emergency_contact_phone: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          email_notifications?: boolean | null
          sms_notifications?: boolean | null
          whatsapp_notifications?: boolean | null
          location_sharing_enabled?: boolean | null
          emergency_contact_email?: string | null
          emergency_contact_phone?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          email_notifications?: boolean | null
          sms_notifications?: boolean | null
          whatsapp_notifications?: boolean | null
          location_sharing_enabled?: boolean | null
          emergency_contact_email?: string | null
          emergency_contact_phone?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      webhook_events: {
        Row: {
          created_at: string | null
          data: Json
          id: string
          processed: boolean | null
          processed_at: string | null
          signature: string | null
          source: string | null
          type: string
        }
        Insert: {
          created_at?: string | null
          data?: Json
          id?: string
          processed?: boolean | null
          processed_at?: string | null
          signature?: string | null
          source?: string | null
          type: string
        }
        Update: {
          created_at?: string | null
          data?: Json
          id?: string
          processed?: boolean | null
          processed_at?: string | null
          signature?: string | null
          source?: string | null
          type?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_guardian_relationship: {
        Args: {
          p_student_id: string
          p_guardian_email: string
          p_guardian_name?: string
          p_guardian_phone?: string
        }
        Returns: boolean
      }
      check_cpf_exists: {
        Args: { p_cpf: string; p_exclude_user_id?: string }
        Returns: boolean
      }
      check_cpf_validation: {
        Args: Record<PropertyKey, never>
        Returns: {
          table_name: string
          total_records: number
          null_cpfs: number
          invalid_cpfs: number
        }[]
      }
      check_guardian_relationship: {
        Args: { guardian_email: string; student_id: string }
        Returns: boolean
      }
      cleanup_expired_invites: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_account_deletion_request: {
        Args: { p_reason?: string }
        Returns: string
      }
      create_guardian_removal_request: {
        Args: {
          p_guardian_id: string
          p_guardian_email: string
          p_reason?: string
        }
        Returns: string
      }
      create_test_location: {
        Args: {
          p_student_id: string
          p_latitude: number
          p_longitude: number
          p_address: string
        }
        Returns: string
      }
      share_guardian_responsibility: {
        Args: {
          p_student_id: string
          p_current_guardian_id: string
          p_new_guardian_email: string
          p_access_level?: string
        }
        Returns: boolean
      }
      remove_shared_guardian: {
        Args: {
          p_student_id: string
          p_current_guardian_id: string
          p_guardian_id_to_remove: string
        }
        Returns: boolean
      }
      execute_guardian_removal: {
        Args: { p_request_id: string }
        Returns: boolean
      }
      format_phone: {
        Args: { phone: string }
        Returns: string
      }
      get_guardian_deletion_requests: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          student_id: string
          student_email: string
          student_name: string
          reason: string
          status: string
          requested_at: string
          processed_at: string
          guardian_notes: string
        }[]
      }
      get_guardian_locations_bypass: {
        Args: { p_student_id: string }
        Returns: Json[]
      }
      get_guardian_locations_bypass_v2: {
        Args: { p_student_id: string }
        Returns: {
          id: string
          user_id: string
          latitude: number
          longitude: number
          location_timestamp: string
          address: string
          shared_with_guardians: boolean
          student_name: string
        }[]
      }
      get_guardian_locations_secure: {
        Args: { p_student_id: string }
        Returns: {
          id: string
          user_id: string
          latitude: number
          longitude: number
          location_timestamp: string
          address: string
          shared_with_guardians: boolean
          student_name: string
        }[]
      }
      get_guardian_notifications: {
        Args: { p_guardian_email: string }
        Returns: {
          id: string
          location_id: string
          student_id: string
          student_name: string
          status: string
          created_at: string
          latitude: number
          longitude: number
        }[]
      }
      get_guardian_removal_requests: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          student_id: string
          guardian_id: string
          guardian_email: string
          student_name: string
          reason: string
          status: string
          requested_at: string
          processed_at: string
          guardian_notes: string
        }[]
      }
      get_guardian_students: {
        Args: Record<PropertyKey, never>
        Returns: {
          student_id: string
          student_email: string
          student_name: string
        }[]
      }
      get_guardian_students_locations: {
        Args: Record<PropertyKey, never>
        Returns: {
          accuracy: number | null
          address: string | null
          battery_level: number | null
          bearing: number | null
          created_at: string | null
          id: string
          is_mocked: boolean | null
          latitude: number
          longitude: number
          shared_with_guardians: boolean | null
          source: string | null
          speed: number | null
          timestamp: string
          user_id: string
        }[]
      }
      get_latest_location_for_all_students: {
        Args: Record<PropertyKey, never>
        Returns: {
          student_id: string
          student_name: string
          latitude: number
          longitude: number
          timestamp: string
          address: string
        }[]
      }
      get_student_guardians_secure: {
        Args: { p_student_id?: string }
        Returns: {
          id: string
          student_id: string
          email: string
          full_name: string
          phone: string
          is_active: boolean
          created_at: string
        }[]
      }
      get_student_locations: {
        Args: { p_guardian_email: string; p_student_id: string }
        Returns: {
          id: string
          user_id: string
          latitude: number
          longitude: number
          location_timestamp: string
          address: string
          student_name: string
          student_email: string
        }[]
      }
      get_student_locations_for_guardian: {
        Args: { input_guardian_id: string }
        Returns: {
          location_id: string
          user_id: string
          latitude: number
          longitude: number
          location_timestamp: string
          address: string
          shared_with_guardians: boolean
          student_name: string
          student_email: string
        }[]
      }
      get_student_locations_with_names: {
        Args: { p_student_id: string; p_time_filter?: unknown }
        Returns: {
          accuracy: number | null
          address: string | null
          battery_level: number | null
          bearing: number | null
          created_at: string | null
          id: string
          is_mocked: boolean | null
          latitude: number
          longitude: number
          shared_with_guardians: boolean | null
          source: string | null
          speed: number | null
          timestamp: string
          user_id: string
        }[]
      }
      get_student_removal_requests: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          student_id: string
          guardian_id: string
          guardian_email: string
          student_name: string
          reason: string
          status: string
          requested_at: string
          processed_at: string
          guardian_notes: string
        }[]
      }
      get_unread_notifications_count: {
        Args: { p_guardian_email: string }
        Returns: number
      }
      get_user_communication_preferences: {
        Args: { p_user_id?: string }
        Returns: {
          user_id: string
          email_enabled: boolean
          sms_enabled: boolean
          whatsapp_enabled: boolean
          phone_number: string
          preferred_method: string
        }[]
      }
      insert_student_test_location: {
        Args: {
          p_student_id: string
          p_latitude?: number
          p_longitude?: number
          p_address?: string
          p_shared?: boolean
        }
        Returns: string
      }
      is_guardian_of_student: {
        Args:
          | Record<PropertyKey, never>
          | { student_id: string; guardian_email: string }
          | { student_id_param: string }
        Returns: boolean
      }
      is_strong_password: {
        Args: { password: string }
        Returns: boolean
      }
      is_valid_cpf: {
        Args: { cpf_input: string } | { cpf_input: string; user_type?: string }
        Returns: boolean
      }
      is_valid_email: {
        Args: { email: string }
        Returns: boolean
      }
      is_valid_guardian: {
        Args: { student_id: string; guardian_email: string }
        Returns: boolean
      }
      is_valid_phone: {
        Args: { phone_number: string }
        Returns: boolean
      }
      log_application_error: {
        Args:
          | Record<PropertyKey, never>
          | {
              p_error_message: string
              p_stack_trace: string
              p_user_id?: string
            }
        Returns: undefined
      }
      log_auth_event: {
        Args: { event_type: string; user_id?: string; metadata?: Json }
        Returns: undefined
      }
      process_account_deletion_request: {
        Args: {
          p_request_id: string
          p_action: string
          p_guardian_notes?: string
        }
        Returns: boolean
      }
      process_guardian_removal_request: {
        Args: {
          p_request_id: string
          p_action: string
          p_guardian_notes?: string
        }
        Returns: boolean
      }
      process_webhook_event: {
        Args: Record<PropertyKey, never> | { event_id: string }
        Returns: boolean
      }
      save_communication_preferences: {
        Args: {
          p_email_enabled?: boolean
          p_sms_enabled?: boolean
          p_whatsapp_enabled?: boolean
          p_phone_number?: string
          p_preferred_method?: string
        }
        Returns: boolean
      }
      save_student_location: {
        Args: {
          p_latitude: number
          p_longitude: number
          p_shared_with_guardians?: boolean
        }
        Returns: string
      }
      test_guardian_access: {
        Args: { p_guardian_email?: string; p_student_id?: string }
        Returns: {
          has_permission: boolean
          guardian_count: number
          location_count: number
          locations_sample: Json
        }[]
      }
      test_location_access: {
        Args: { p_student_id: string }
        Returns: {
          id: string
          user_id: string
          latitude: number
          longitude: number
          location_timestamp: string
          address: string
          shared_with_guardians: boolean
          student_name: string
          student_user_id: string
        }[]
      }
      validate_email: {
        Args: { email: string }
        Returns: boolean
      }
      validate_phone: {
        Args: { phone: string }
        Returns: boolean
      }
      verify_user_integrity: {
        Args: Record<PropertyKey, never>
        Returns: {
          email: string
          auth_exists: boolean
          user_exists: boolean
          profile_exists: boolean
          missing_data: string
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
