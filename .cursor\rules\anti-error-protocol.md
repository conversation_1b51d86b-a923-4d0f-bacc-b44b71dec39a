# 🚫 Anti-Error & Anti-Duplication Protocol

## Core Rule: EVIDENCE-FIRST APPROACH
```
🚫 NEVER assert problems without concrete evidence
✅ ALWAYS verify with tools before concluding
🚫 NEVER assume files are broken if the app is working
✅ ALWAYS investigate before creating duplicate code
```

## Verification Commands (Execute BEFORE any diagnosis):
```bash
# Check real structure
ls src/pages/
find src/ -name "*.tsx" | head -10

# Check if it's working
npm run build    # Real errors?
npm run dev      # Loads without error?

# Check for existing functionality  
find src/ -name "*.tsx" -o -name "*.ts" | grep -i [related-term]
grep -r "similar_function" src/
ls src/components/ | grep -i [functionality]
```

## Decision Matrix:
| Evidence | Action |
|----------|--------|
| ✅ Server running + Build OK | ❌ DON'T assume problems |
| ✅ Files exist | ❌ DON'T say they're missing |
| ✅ App loads | ❌ DON'T invent broken imports |
| ❌ Real error shown | ✅ Analyze and fix |

## Anti-Duplication Checklist:
Before creating ANY file:
- [ ] Searched for similar functionality?
- [ ] Checked if implementation already exists?
- [ ] Analyzed current project structure?
- [ ] Identified where this function might ALREADY be?
- [ ] Considered EXTENDING instead of DUPLICATING?

## Strategy Priority:
1. 🔧 **REUSE** - Use what already exists
2. 📈 **EXTEND** - Improve/expand existing
3. 🔄 **REFACTOR** - Reorganize current code
4. ➕ **CREATE** - Only if really necessary

## Banned Phrases (Trigger Verification):
- "it seems that..."
- "probably..."
- "should be..."
- "it's possible that..."

When using these, STOP and verify with evidence first! 