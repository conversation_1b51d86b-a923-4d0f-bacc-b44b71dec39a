# Student Management Panel for Parent Dashboard

## Project Overview
This report analyzes the implementation of a centralized student management panel within the parent dashboard. The panel, available at `/parent-dashboard/students`, will allow guardians to manage their linked students in a single interface.

## Current Situation Analysis
The existing parent dashboard includes:
- Sidebar menu navigation
- List of linked students
- Map with locations
- Location history with accuracy, address and timestamp
- A basic "Add Student" button

However, management features are scattered and there is no unified place to edit profiles, set permissions or manage notifications.

## Proposed Solution: Student Management Panel
### Key Features
1. **Centralized Student Management**
   - Add new students
   - Edit profiles (name, school, grade, class)
   - Remove linked students
2. **Per-Student Permission Configuration**
   - Monitoring schedules
   - Geofence setup (safe or alert areas)
   - Location sharing control
3. **Notification Management**
   - Geofence entry/exit alerts
   - Low battery notifications
   - Inactivity alerts

## Required Components
1. **StudentManagementPanel** – main component integrating features
2. **StudentForm** – add/edit form
3. **StudentDetails** – view and update student info
4. **StudentList** – navigation list with status indicators
5. **Additional Components** – `GeofenceManager`, `NotificationSettings`, `MonitoringSchedule`

## Services and Integrations
- `student-management-service.ts` for CRUD operations
- `location-service.ts` for geofence management
- MapBox API for drawing and viewing geofences
- Notification system for guardian alerts

## Backend Integration
- New tables for geofences and notification settings
- APIs for permissions
- Stored procedures for monitoring rules
- RLS policies ensuring authorized access only
- Edge Functions for location events and geofence checks

## Technical Challenges
1. **CORS with `nominatim.openstreetmap.org`** – needs resolution
2. **Service Worker** – cache strategy issues
3. **MapBox Integration** – implement geofence drawing and event processing

## Recommended Implementation Steps
1. **Phase 1:** Create route and basic CRUD
2. **Phase 2:** Add geofences and monitoring schedules
3. **Phase 3:** Implement notification system
4. **Phase 4:** Test and optimize

## Security Considerations
- Protect location data via RLS
- Profile-based access control
- Audit configuration changes

## Compatibility
- Works with existing PKCE authentication
- Responsive design for mobile
- Integrates with current Resend email services

## Next Steps
1. Define interface prototypes
2. Update data models
3. Adjust RLS policies
4. Resolve CORS issues

*Date: June 06, 2025*
*Author: Development Team*
