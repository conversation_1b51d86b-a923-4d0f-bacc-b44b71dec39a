# Development Practices - Locate-Family-Connect

## 🛡️ Princípios Fundamentais

### O Mantra de Segurança

> **"TODA mudança deve ser COMPROVADAMENTE SEGURA antes de ser aplicada"**

### 🔍 Princípios Centrais

- **EVIDÊNCIA-PRIMEIRO**: Verificar antes de assumir problemas
- **TESTE-DRIVEN**: Testar antes de alterar código
- **INCREMENTAL**: Mudanças pequenas e controláveis
- **REVERSÍVEL**: Manter sempre um caminho de rollback
- **COMPATIBILIDADE**: Preservar compatibilidade durante transições

## 🚫 Protocolo Anti-Erro (OBRIGATÓRIO)

### Verificação Obrigatória Antes de Qualquer Análise

```bash
# Verificação de integridade do projeto
ls src/pages/ && npm run build && npm run dev

# Busca por arquivos relacionados à funcionalidade
find src/ -name "*.tsx" | grep -i [functionality]

# Busca por funções similares
grep -r "similar_function" src/
```

### Regra de Decisão

- Se servidor roda + build funciona = NÃO assumir problemas
- Se arquivos existem = NÃO dizer que estão faltando
- Antes de criar = PROCURAR funcionalidade existente

## 📏 Convenções e Padrões

### Estrutura de Código

- Limitar arquivos a 300 linhas
- Separar arquivos por função/propósito
- Manter consistência na estrutura de pastas por perfil (`student/`, `guardian/`)

### Nomenclatura

- **Variáveis/Funções**: camelCase 
- **Componentes React**: PascalCase
- **Arquivos de Componentes**: PascalCase.tsx
- **Hooks**: use[Nome]
- **Contextos**: [Nome]Context
- **Tipos/Interfaces**: I[Nome] ou T[Nome]
- **Constantes Globais**: UPPER_SNAKE_CASE
- **Funções SQL/Banco**: snake_case

### TypeScript

- Sempre tipar funções e componentes
- Definir interfaces para props de componentes
- Evitar `any` e type assertions (`as`)
- Extrair tipos complexos para arquivos dedicados em `/types`

## 🧩 Componentização e Reuso

### Custom Hooks 

- Extrair lógica reutilizável para hooks
- Nomenclatura: `use[Feature]` (ex: `useMapInitialization`)
- Centralizar em `/src/hooks/`

### Composição vs Herança

- Preferir composição sobre herança para componentes React
- Utilizar render props ou children para customização
- Criar componentes atômicos e compô-los

### Utilitários e Helpers

- Centralizar em `/src/lib/utils/`
- Manter funções puras quando possível
- Documentar parâmetros e retornos

## 🔄 Controle de Versão

### Automação Git

```bash
git status
git add -A
git commit -m "sua mensagem detalhada [tipo: fix/feature/docs]"
git push
```

### Estrutura de Commits

- Prefixos de tipo: `fix:`, `feat:`, `docs:`, `refactor:`, `test:`, `chore:`
- Descrição clara e específica
- Referência a issues quando aplicável (#123)

### Branches

- `main` - Produção
- `develop` - Integração
- `feature/nome-feature` - Novas funcionalidades
- `fix/nome-fix` - Correções de bugs
- `refactor/nome-refactor` - Refatorações

## 🧪 Testes e Qualidade

### Testes Automatizados

- Testes unitários com Jest
- Testes de componente com React Testing Library
- E2E com Cypress

### Checklist de Qualidade

- Código compila sem warnings
- Testes passam
- Funciona nos principais navegadores
- Responsivo (mobile, tablet, desktop)
- Acessível (keyboard navigation, screen readers)

## 🚀 CI/CD

### Verificações Automáticas

- Linting via ESLint
- Type checking com TypeScript
- Testes unitários
- Build de produção

## 📊 Monitoramento e Logging

### Estratégia de Logs

- Nível apropriado (debug, info, warn, error)
- Contexto suficiente
- Estruturação consistente
- Evitar informações sensíveis

## 🔒 Segurança

### Melhores Práticas

- Nunca armazenar credenciais no código
- Utilizar variáveis de ambiente (.env)
- Sanitizar inputs do usuário
- Implementar CSRF protection
- Validar permissions via RLS
- Manter dependências atualizadas

## 🧠 Planner Mode

Ao iniciar novas funcionalidades, seguir o processo:

1. Refletir sobre impactos no fluxo de autenticação e compartilhamento de localização
2. Revisar `UnifiedAuthContext.tsx`, Edge Functions e políticas RLS relacionadas
3. Verificar implicações para ambos os perfis (guardian/student)
4. Avaliar impacto em integrações Resend e MapBox
5. Verificar potenciais conflitos com triggers e funções SQL existentes
6. Considerar impacto nas políticas de Row Level Security
7. Propor um plano focado em segurança e compatibilidade
8. Implementar em etapas, testando cada perfil separadamente

## 🐞 Debugger Mode

Para solução de problemas, verificar:

1. Problemas em integrações críticas:
   - Autenticação Supabase (PKCE)
   - Emails via Resend (verificação de domínio)
   - Edge Function `share-location`
   - Políticas RLS em `locations` e `guardians`

## 📚 Referências

- [Guia Completo de Desenvolvimento](./DEVELOPMENT_GUIDELINES.md)
- [Análise de Erros](./ANALISE_ERROS_SISTEMA.md)
- [Boas Práticas](./BOAS_PRATICAS_DESENVOLVIMENTO.md)
- [Protocolo Anti-Quebra](./ANTI_BREAK_PROTOCOL.md)
