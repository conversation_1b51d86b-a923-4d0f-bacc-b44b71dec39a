
import { createContext, useState, useEffect, useContext } from 'react';
import { supabase } from '@/lib/supabase';
import { asDbColumn } from '@/lib/utils/type-casting-helpers';
import AsyncStorage from '@/lib/mocks/async-storage-mock'; // Use our mock implementation

// Define the user type
export interface ExtendedUser {
  id: string;
  email: string;
  user_type?: string;
  user_metadata?: {
    user_type?: string;
    full_name?: string;
    [key: string]: any;
  };
  app_metadata?: {
    user_type?: string;
    [key: string]: any;
  };
}

// Define the auth context type
interface AuthContextType {
  user: ExtendedUser | null;
  loading: boolean;
  error: any | null;
  signIn: (email: string, password: string) => Promise<{ data?: any; error: any | null }>;
  signOut: () => Promise<void>;
  signUp: (email: string, password: string, userData: object) => Promise<{ error: any | null }>;
  forgotPassword: (email: string) => Promise<{ error: any | null }>;
}

// Create the context
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Create the provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<ExtendedUser | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<any | null>(null);

  useEffect(() => {
    // Check for existing session
    const checkSession = async () => {
      setLoading(true);
      try {
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          throw error;
        }

        if (data.session) {
          // Get user profile from profiles table
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('user_type, full_name')
            .eq(asDbColumn('user_id'), data.session.user.id)
            .single();

          // Create extended user with profile data
          const extendedUser: ExtendedUser = {
            ...data.session.user,
            user_type: profileData?.user_type
          };
          
          setUser(extendedUser);
        } else {
          setUser(null);
        }
      } catch (err) {
        console.error('Session check error:', err);
        setError(err);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkSession();

    // Subscribe to auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event);
        
        if (session && session.user) {
          // Get user profile when auth state changes
          try {
            const { data: profileData } = await supabase
              .from('profiles')
              .select('user_type, full_name')
              .eq(asDbColumn('user_id'), session.user.id)
              .single();

            const extendedUser: ExtendedUser = {
              ...session.user,
              user_type: profileData?.user_type
            };
            
            setUser(extendedUser);
          } catch (err) {
            console.error('Error fetching profile:', err);
            // Still set the user even if profile fetch fails
            setUser(session.user as ExtendedUser);
          }
        } else {
          setUser(null);
        }
        
        setLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Função auxiliar para mapear erros do Supabase para mensagens amigáveis
  const getAuthErrorMessage = (error: any): string => {
    // Definir mensagens de erro personalizadas com base no código ou mensagem
    const errorMap: Record<string, string> = {
      'Invalid login credentials': 'E-mail ou senha incorretos. Verifique suas credenciais.',
      'Email not confirmed': 'E-mail não confirmado. Verifique sua caixa de entrada para confirmar seu cadastro.',
      'Invalid email': 'E-mail inválido. Verifique se o formato está correto.',
      'User not found': 'Usuário não encontrado. Verifique o e-mail ou cadastre-se.',
      'Password is too short': 'A senha deve ter pelo menos 6 caracteres.',
      'Password is too weak': 'A senha é muito fraca. Use letras, números e caracteres especiais.',
      'Email already in use': 'Este e-mail já está sendo utilizado por outra conta.',
      'Invalid captcha': 'Verificação de segurança inválida. Tente novamente.',
      'Rate limit exceeded': 'Muitas tentativas. Aguarde alguns minutos e tente novamente.',
      'Email rate limit exceeded': 'Muitos e-mails enviados. Aguarde alguns minutos.',
      'Refresh Token Not Found': 'Sessão expirada ou inválida. Faça login novamente.',
    };

    // Identificar mensagem de erro com base no código ou mensagem original
    const originalMessage = error?.message || error?.error_description || String(error);

    // Percorrer as chaves do mapa de erros para encontrar correspondências parciais
    for (const key in errorMap) {
      if (originalMessage.includes(key)) {
        return errorMap[key];
      }
    }

    // Mensagem padrão se não encontrar correspondências específicas
    return 'Ocorreu um erro durante a autenticação. Tente novamente mais tarde.';
  };

  // Sign in function
  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);

      // Validação básica de entrada
      if (!email.trim()) {
        const error = { message: 'E-mail não pode estar vazio', name: 'ValidationError' };
        setError(error);
        return { error };
      }

      if (!password.trim()) {
        const error = { message: 'Senha não pode estar vazia', name: 'ValidationError' };
        setError(error);
        return { error };
      }

      // Limpar whitespace para evitar problemas de autenticação
      const trimmedEmail = email.trim();
      const trimmedPassword = password.trim();
      
      console.log(`[MCP][Auth] Tentando login com email: ${trimmedEmail.slice(0, 3)}...`);
      
      const result = await supabase.auth.signInWithPassword({ 
        email: trimmedEmail, 
        password: trimmedPassword 
      });
      
      if (result.error) {
        console.error('[MCP][Auth] Erro de login:', result.error.message);
        
        // Usar o helper para obter mensagem amigável
        const friendlyMessage = getAuthErrorMessage(result.error);
        
        // Criar um erro enriquecido com a mensagem amigável
        const enhancedError = { 
          ...result.error, 
          friendlyMessage,
          original: result.error.message 
        };
        
        setError(enhancedError);
        return { error: enhancedError };
      }
      
      console.log('[MCP][Auth] Login bem-sucedido');
      return { data: result.data, error: null };
    } catch (err) {
      console.error('[MCP][Auth] Erro não tratado durante login:', err);
      
      // Criar erro com mensagem amigável mesmo para exceções não esperadas
      const friendlyMessage = getAuthErrorMessage(err);
      const enhancedError = { 
        ...err, 
        friendlyMessage,
        original: err instanceof Error ? err.message : String(err) 
      };
      
      setError(enhancedError);
      return { error: enhancedError };
    } finally {
      setLoading(false);
    }
  };

  // Sign out function
  const signOut = async () => {
    setLoading(true);
    try {
      await supabase.auth.signOut();
      setUser(null);
    } catch (err) {
      console.error('Sign out error:', err);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  // Sign up function
  const signUp = async (email: string, password: string, userData: object) => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData
        }
      });
      
      if (error) {
        setError(error);
      }
      
      return { error };
    } catch (err) {
      console.error('Sign up error:', err);
      setError(err);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };

  // Forgot password function
  const forgotPassword = async (email: string) => {
    setLoading(true);
    try {
      // Validação básica de e-mail
      if (!email.trim()) {
        const error = { message: 'E-mail não pode estar vazio', name: 'ValidationError' };
        setError(error);
        return { error };
      }

      // Validação básica de formato de e-mail
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email.trim())) {
        const error = { message: 'Formato de e-mail inválido', name: 'ValidationError' };
        setError(error);
        return { error };
      }
      
      console.log(`[MCP][Auth] Solicitando redefinição de senha para: ${email.trim().slice(0, 3)}...`);

      // Configuração detalhada do redirecionamento
      // Usando caminho absoluto e garantindo que seja completo
      const redirectUrl = `${window.location.origin}/reset-password`;
      console.log(`[MCP][Auth] URL de redirecionamento para redefinição: ${redirectUrl}`);
      
      // Adicionar parâmetros extra para ajudar no diagnóstico
      const redirectWithMetadata = new URL(redirectUrl);
      redirectWithMetadata.searchParams.append('source', 'email_recovery');
      
      console.log(`[MCP][Auth] URL final com metadados: ${redirectWithMetadata.toString()}`);
      
      const { error } = await supabase.auth.resetPasswordForEmail(email.trim(), {
        redirectTo: redirectWithMetadata.toString(),
      });
      
      if (error) {
        console.error('[MCP][Auth] Erro ao solicitar redefinição de senha:', error.message);
        
        // Usar o helper para obter mensagem amigável
        const friendlyMessage = getAuthErrorMessage(error);
        
        // Criar um erro enriquecido com a mensagem amigável
        const enhancedError = { 
          ...error, 
          friendlyMessage,
          original: error.message 
        };
        
        setError(enhancedError);
        return { error: enhancedError };
      }
      
      console.log('[MCP][Auth] E-mail de redefinição de senha enviado com sucesso');
      return { error: null };
    } catch (err) {
      console.error('[MCP][Auth] Erro não tratado ao solicitar redefinição de senha:', err);
      
      // Criar erro com mensagem amigável mesmo para exceções não esperadas
      const friendlyMessage = getAuthErrorMessage(err);
      const enhancedError = { 
        ...err, 
        friendlyMessage,
        original: err instanceof Error ? err.message : String(err) 
      };
      
      setError(enhancedError);
      return { error: enhancedError };
    } finally {
      setLoading(false);
    }
  };

  // Define the context value
  const value = {
    user,
    loading,
    error,
    signIn,
    signOut,
    signUp,
    forgotPassword
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook for using the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Default export for the AuthProvider component
export default AuthProvider;
