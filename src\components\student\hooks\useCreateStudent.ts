import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { createStudentWithGuardian } from '@/lib/services/student/student-management-service';
import type { NewStudent } from '@/types/student';

interface CreateStudentResult {
  success: boolean;
  error?: string;
  data?: any;
}

export function useCreateStudent() {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleCreateStudent = async (
    student: NewStudent
  ): Promise<CreateStudentResult> => {
    setLoading(true);
    try {
      const data = await createStudentWithG<PERSON>ian(student);
      toast({
        title: 'Sucesso',
        description: 'Estudante adicionado com sucesso!',
      });
      return { success: true, data };
    } catch (error: any) {
      console.error('Error creating student:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Erro ao adicionar estudante',
        variant: 'destructive',
      });
      return { success: false, error: error.message || 'Erro ao adicionar estudante' };
    } finally {
      setLoading(false);
    }
  };

  return { loading, handleCreateStudent };
}

export default useCreateStudent;
