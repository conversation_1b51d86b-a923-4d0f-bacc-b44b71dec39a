
import { supabase } from '@/lib/supabase';
import { GuardianData } from '@/types/database';

export class GuardianService {
  async getStudentGuardians(studentId: string): Promise<GuardianData[]> {
    try {
      const { data, error } = await supabase.rpc(
        'get_student_guardians_secure',
        { p_student_id: studentId }
      );

      if (error) {
        throw error;
      }

      return (
        data?.map((guardian: any) => ({
          id: guardian.id,
          full_name: guardian.full_name,
          email: guardian.email,
          phone: guardian.phone || undefined,
          created_at: guardian.created_at,
          is_active: guardian.is_active || false,
          student_id: guardian.student_id || undefined,
          birth_date: guardian.birth_date || undefined,
          cpf: guardian.cpf || undefined,
          status: guardian.status || undefined,
          updated_at: guardian.updated_at || undefined,
          guardian_id: guardian.id,
          relationship_type: 'guardian'
        })) || []
      );
    } catch (error) {
      console.error('Error fetching guardians:', error);
      return [];
    }
  }

  async addGuardian(guardianData: Omit<GuardianData, 'id' | 'created_at'>): Promise<{ success: boolean; message?: string }> {
    try {
      const { error } = await supabase
        .from('guardians')
        .insert([{
          student_id: guardianData.student_id,
          email: guardianData.email,
          full_name: guardianData.full_name,
          phone: guardianData.phone || null,
          is_active: true
        }]);

      if (error) {
        throw error;
      }

      return { success: true };
    } catch (error: any) {
      console.error('Error adding guardian:', error);
      return { success: false, message: error.message };
    }
  }
}

export const guardianService = new GuardianService();
