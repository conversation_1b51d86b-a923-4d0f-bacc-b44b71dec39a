import { Student, StudentUpdate } from '@/types/student';
import { GuardianSharing, PermissionUpdate } from '@/types/permissions';
import { toast } from '@/components/ui/use-toast';
import { StudentManagementService } from './student-management-service';
import {
  fetchStudentPermissions,
  updateStudentPermissions,
} from './student-permissions-service';
import { supabase } from '@/lib/supabase';

export class StudentPermissionsManager {
  // Fetch a student profile together with its permissions
  async fetchStudentWithPermissions(studentId: string, guardianId: string): Promise<Student | null> {
    try {
      const student = await StudentManagementService.getStudentById(studentId, guardianId);
      if (!student) return null;

      const permissions = await fetchStudentPermissions(studentId, guardianId);

      return { ...student, permissions };
    } catch (error) {
      console.error('[StudentPermissionsManager] fetchStudentWithPermissions', error);
      return null;
    }
  }

  // Update student profile and its permissions in one call
  async updateStudentWithPermissions(
    studentId: string,
    guardianId: string,
    updates: StudentUpdate & { permissions?: PermissionUpdate }
  ): Promise<Student> {
    try {
      const { permissions: permissionUpdates, ...studentUpdates } = updates;

      const updatedStudent = await StudentManagementService.updateStudent(studentId, studentUpdates, guardianId);
      if (!updatedStudent) {
        throw new Error('Failed to update student');
      }

      if (permissionUpdates) {
        await updateStudentPermissions(studentId, guardianId, permissionUpdates);
      }

      const permissions = await fetchStudentPermissions(studentId, guardianId);

      return { ...updatedStudent, permissions };
    } catch (error: any) {
      toast({ title: 'Erro', description: error.message || 'Falha ao atualizar permissões', variant: 'destructive' });
      throw error;
    }
  }

  // Update only guardian sharing options
  async updateGuardianSharing(
    studentId: string,
    guardianId: string,
    updates: Partial<GuardianSharing>
  ): Promise<boolean> {
    try {
      await updateStudentPermissions(studentId, guardianId, {
        guardianSharing: updates,
      });
      return true;
    } catch (error: any) {
      toast({ title: 'Erro', description: error.message || 'Falha ao atualizar compartilhamento', variant: 'destructive' });
      throw error;
    }
  }

  // Share access with a new guardian using Supabase RPC
  async addSharedGuardian(
    studentId: string,
    currentGuardianId: string,
    newGuardianEmail: string,
    accessLevel: 'full' | 'limited' | 'readonly' = 'readonly'
  ): Promise<boolean> {
    try {
      const permissions = await fetchStudentPermissions(studentId, currentGuardianId);
      if (!permissions.guardianSharing.enabled || permissions.guardianSharing.accessLevel !== 'full') {
        throw new Error('Permissão negada para compartilhar');
      }

      const { error } = await supabase.rpc('share_guardian_responsibility', {
        p_student_id: studentId,
        p_current_guardian_id: currentGuardianId,
        p_new_guardian_email: newGuardianEmail,
        p_access_level: accessLevel,
      });

      if (error) {
        throw error;
      }

      return true;
    } catch (error: any) {
      toast({ title: 'Erro', description: error.message || 'Falha ao adicionar responsável', variant: 'destructive' });
      throw error;
    }
  }

  // Remove a previously shared guardian
  async removeSharedGuardian(
    studentId: string,
    currentGuardianId: string,
    guardianIdToRemove: string
  ): Promise<boolean> {
    try {
      const permissions = await fetchStudentPermissions(studentId, currentGuardianId);
      if (!permissions.guardianSharing.enabled || permissions.guardianSharing.accessLevel !== 'full') {
        throw new Error('Permissão negada para remover responsáveis');
      }

      const { error } = await supabase.rpc('remove_shared_guardian', {
        p_student_id: studentId,
        p_current_guardian_id: currentGuardianId,
        p_guardian_id_to_remove: guardianIdToRemove,
      });

      if (error) {
        throw error;
      }

      return true;
    } catch (error: any) {
      toast({ title: 'Erro', description: error.message || 'Falha ao remover responsável', variant: 'destructive' });
      throw error;
    }
  }
}
