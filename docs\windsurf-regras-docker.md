# 🚫 REGRAS WINDSURF: DOCKER BUILD/RUN - PROTOCOLOS DE PREVENÇÃO

## 🔴 REGRAS MANDATÓRIAS PARA DOCKER BUILD

```
1. SEMPRE verifique a estrutura de assets estáticos ANTES de build
2. NUNCA assuma que todos arquivos estão disponíveis em /dist
3. SEMPRE use --no-cache nas primeiras tentativas de build após falhas
4. NUNCA execute docker-compose up sem primeiro fazer down
5. SEMPRE use o comando serve com opção -s para servir SPAs corretamente
```

## 🔍 VERIFICAÇÕES OBRIGATÓRIAS

### Antes de qualquer build Docker:
- Verificar se public/icons/ contém todos os ícones do manifest.json
- Confirmar que todos os arquivos mencionados em manifest.json existem
- Limpar qualquer processo rodando na porta 8080
- Confirmar que o serviço Supabase está acessível localmente

### Durante build Docker:
- Monitorar logs para erros de módulos não encontrados
- Verificar mensagens de path resolution
- Confirmar criação correta do diretório /dist

### Após build Docker:
- Verificar arquivos dentro do contêiner com `docker exec -it [container] ls -la /app/dist`
- Testar manualmente navegação para páginas críticas
- Inspecionar erros de console relacionados a recursos não encontrados

## 🧪 PROTOCOLO DE DIAGNÓSTICO DOCKER

1. Verificar status do contêiner: `docker ps -a`
2. Examinar logs detalhados: `docker logs [container] --tail 100`
3. Acessar shell do contêiner: `docker exec -it [container] /bin/sh`
4. Verificar arquivos dentro do contêiner: `ls -la /app/dist`
5. Testar servidor dentro do contêiner: `npx serve -s /app/dist -l 8080`
6. Inspecionar network do Docker: `docker network inspect [network]`

## 🚑 DIAGNÓSTICO RÁPIDO

Para erros de assets estáticos:
```
1. Verificar se o asset existe em: public/icons/
2. Confirmar que o path no manifest.json está correto
3. Verificar se o asset foi copiado para /dist durante o build
4. Confirmar se o serve está servindo arquivos estáticos corretamente
```

Para erros de build Docker:
```
1. Limpar imagens e contêineres: docker system prune -a
2. Reconstruir com --no-cache: docker compose build --no-cache
3. Verificar espaço em disco: df -h
4. Checar permissões no diretório do projeto
```

## 📋 FLUXO DE TRABALHO CORRETO

```bash
# 1. Parar todos os contêineres
docker compose down

# 2. Limpar cache e volumes problemáticos
docker system prune --volumes

# 3. Reconstruir sem cache
docker compose build --no-cache

# 4. Iniciar em modo detached
docker compose up -d

# 5. Verificar logs
docker logs -f [container-name]

# 6. Acessar o contêiner para debug
docker exec -it [container-name] /bin/sh
```
