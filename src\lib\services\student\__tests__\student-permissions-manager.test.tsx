import { StudentPermissionsManager } from '../student-permissions-manager';
import { StudentManagementService } from '../student-management-service';
import * as permService from '../student-permissions-service';
import { supabase } from '@/lib/supabase';
import { Student } from '@/types/student';
import { StudentPermissions } from '@/types/permissions';

jest.mock('../student-management-service');
jest.mock('../student-permissions-service');

describe('StudentPermissionsManager', () => {
  const manager = new StudentPermissionsManager();

  const baseStudent: Student = {
    id: '1',
    user_id: '1',
    name: 'Test',
    schoolId: '',
    schoolName: '',
    grade: '',
    class: '',
    status: 'active',
    guardianId: 'g1',
    locationSharing: true,
    lastLocation: null,
    createdAt: 'now',
    updatedAt: 'now',
  };

  const basePermissions: StudentPermissions = {
    studentId: '1',
    guardianId: 'g1',
    monitoringSchedule: [],
    geofences: [],
    notificationPreferences: {
      enterGeofence: true,
      exitGeofence: true,
      lowBattery: true,
      locationUpdate: true,
      offlineAlert: true,
      notificationChannels: { email: true, push: true, sms: false },
    },
    guardianSharing: {
      enabled: true,
      accessLevel: 'full',
      maxGuardians: 3,
      sharedWith: [],
    },
    createdAt: 'now',
    updatedAt: 'now',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('fetches student with permissions', async () => {
    jest.spyOn(StudentManagementService, 'getStudentById').mockResolvedValue(baseStudent);
    jest.spyOn(permService, 'fetchStudentPermissions').mockResolvedValue(basePermissions);

    const result = await manager.fetchStudentWithPermissions('1', 'g1');

    expect(result).toEqual({ ...baseStudent, permissions: basePermissions });
    expect(StudentManagementService.getStudentById).toHaveBeenCalledWith('1', 'g1');
    expect(permService.fetchStudentPermissions).toHaveBeenCalledWith('1', 'g1');
  });

  it('updates student and permissions', async () => {
    jest.spyOn(StudentManagementService, 'updateStudent').mockResolvedValue({ ...baseStudent, name: 'New' });
    jest.spyOn(permService, 'updateStudentPermissions').mockResolvedValue(basePermissions);
    jest.spyOn(permService, 'fetchStudentPermissions').mockResolvedValue(basePermissions);

    const result = await manager.updateStudentWithPermissions('1', 'g1', {
      name: 'New',
      permissions: { notificationPreferences: { enterGeofence: false } },
    });

    expect(StudentManagementService.updateStudent).toHaveBeenCalledWith('1', { name: 'New' }, 'g1');
    expect(permService.updateStudentPermissions).toHaveBeenCalledWith('1', 'g1', { notificationPreferences: { enterGeofence: false } });
    expect(result).toEqual({ ...baseStudent, name: 'New', permissions: basePermissions });
  });

  it('adds shared guardian when permitted', async () => {
    jest.spyOn(permService, 'fetchStudentPermissions').mockResolvedValue(basePermissions);
    const rpcMock = jest.fn().mockResolvedValue({ data: null, error: null });
    (supabase as any).rpc = rpcMock;

    await manager.addSharedGuardian('1', 'g1', '<EMAIL>', 'limited');

    expect(rpcMock).toHaveBeenCalledWith('share_guardian_responsibility', {
      p_student_id: '1',
      p_current_guardian_id: 'g1',
      p_new_guardian_email: '<EMAIL>',
      p_access_level: 'limited',
    });
  });

  it('removes shared guardian when permitted', async () => {
    jest.spyOn(permService, 'fetchStudentPermissions').mockResolvedValue(basePermissions);
    const rpcMock = jest.fn().mockResolvedValue({ data: null, error: null });
    (supabase as any).rpc = rpcMock;

    await manager.removeSharedGuardian('1', 'g1', 'g2');

    expect(rpcMock).toHaveBeenCalledWith('remove_shared_guardian', {
      p_student_id: '1',
      p_current_guardian_id: 'g1',
      p_guardian_id_to_remove: 'g2',
    });
  });
});
