
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { useCreateStudent } from '../hooks/useCreateStudent';

interface StudentFormProps {
  onSuccess?: () => void;
}

export const StudentForm: React.FC<StudentFormProps> = ({ onSuccess }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    schoolName: '',
    grade: '',
    class: ''
  });
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useUser();
  const { loading: creating, handleCreateStudent } = useCreateStudent();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (!user?.id) {
        throw new Error('Usuário não autenticado');
      }

      const result = await handleCreateStudent({
        name: formData.name,
        schoolId: '',
        schoolName: formData.schoolName,
        grade: formData.grade,
        class: formData.class,
        guardianId: user.id,
      });

      if (result.success) {
        onSuccess?.();
      }
    } catch (error: any) {
      console.error('Error creating student:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível adicionar o estudante.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="name">Nome</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />
      </div>

      <div>
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          required
        />
      </div>

      <div>
        <Label htmlFor="schoolName">Escola</Label>
        <Input
          id="schoolName"
          value={formData.schoolName}
          onChange={(e) => setFormData({ ...formData, schoolName: e.target.value })}
          required
        />
      </div>

      <div>
        <Label htmlFor="grade">Série</Label>
        <Select value={formData.grade} onValueChange={(value) => setFormData({ ...formData, grade: value })}>
          <SelectTrigger>
            <SelectValue placeholder="Selecione a série" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">1º Ano</SelectItem>
            <SelectItem value="2">2º Ano</SelectItem>
            <SelectItem value="3">3º Ano</SelectItem>
            <SelectItem value="4">4º Ano</SelectItem>
            <SelectItem value="5">5º Ano</SelectItem>
            <SelectItem value="6">6º Ano</SelectItem>
            <SelectItem value="7">7º Ano</SelectItem>
            <SelectItem value="8">8º Ano</SelectItem>
            <SelectItem value="9">9º Ano</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="class">Turma</Label>
        <Input
          id="class"
          value={formData.class}
          onChange={(e) => setFormData({ ...formData, class: e.target.value })}
          required
        />
      </div>

      <Button type="submit" disabled={loading || creating} className="w-full">
        {loading || creating ? 'Adicionando...' : 'Adicionar Estudante'}
      </Button>
    </form>
  );
};
