-- Create table for storing user communication preferences
CREATE TABLE IF NOT EXISTS public.communication_preferences (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  email_notifications boolean DEFAULT true,
  sms_notifications boolean DEFAULT false,
  whatsapp_notifications boolean DEFAULT false,
  location_sharing_enabled boolean DEFAULT false,
  emergency_contact_email text,
  emergency_contact_phone text,
  created_at timestamptz NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamptz NOT NULL DEFAULT timezone('utc'::text, now())
);

ALTER TABLE public.communication_preferences ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own communication preferences"
  ON public.communication_preferences
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can upsert own communication preferences"
  ON public.communication_preferences
  FOR ALL USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

CREATE TRIGGER set_communication_preferences_updated_at
  BEFORE UPDATE ON public.communication_preferences
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
