# Relatório Completo do Banco de Dados – Supabase (Projeto Reno)

## Visão Geral
Este relatório apresenta a estrutura do banco de dados Supabase, com foco especial em usuários, relacionamentos e dados principais do app EduConnect.

---

## 1. Tabelas Principais e Relacionamentos

### a) **profiles** (Perfil de Usuário)
- **Campos:**
  - id (PK, int)
  - user_id (uuid, FK → auth.users.id)
  - full_name, phone, user_type, email, cpf, birth_date, parent_email, parent_cpf, registration_status, requires_parent_confirmation, status, last_login_at, login_count, created_at, updated_at
- **Relacionamentos:**
  - user_id → auth.users.id
- **Observações:**
  - Armazena dados de perfil de todos os usuários (estudantes, responsáveis, etc).

### b) **guardians** (Responsáveis)
- **Campos:**
  - id (PK, uuid)
  - student_id (uuid, FK → auth.users.id)
  - full_name, email, phone, is_active, cpf, birth_date, status, created_at, updated_at
- **Relacionamentos:**
  - student_id → auth.users.id
- **Observações:**
  - Relaciona responsáveis a estudantes. O campo is_active indica vínculo atual.

### c) **students**
- **Campos:**
  - id (PK, uuid)
  - name, school_id, school_name, grade, class, status, guardian_id, location_sharing, last_location, created_at, updated_at
- **Relacionamentos:**
  - guardian_id → auth.users.id
- **Observações:**
  - Cadastro de estudantes, com referência ao responsável principal.

### d) **student_guardian_relationships**
- **Campos:**
  - id (PK, uuid)
  - student_id (uuid, FK → auth.users.id)
  - guardian_id (uuid, FK → auth.users.id)
  - relationship_type, is_primary, created_at, updated_at
- **Relacionamentos:**
  - student_id → auth.users.id
  - guardian_id → auth.users.id
- **Observações:**
  - Permite múltiplos responsáveis por estudante e define o tipo de vínculo.

### e) **locations**
- **Campos:**
  - id (PK, uuid)
  - user_id (uuid, FK → auth.users.id)
  - latitude, longitude, timestamp, address, shared_with_guardians, accuracy, speed, bearing, battery_level, is_mocked, source, created_at
- **Relacionamentos:**
  - user_id → auth.users.id
- **Observações:**
  - Armazena localizações dos usuários (principalmente estudantes).

### f) **location_history**
- **Campos:**
  - id (PK, uuid)
  - location_id (uuid, FK → locations.id)
  - user_id (uuid, FK → auth.users.id)
  - latitude, longitude, accuracy, speed, bearing, battery_level, is_mocked, source, created_at
- **Relacionamentos:**
  - location_id → locations.id
  - user_id → auth.users.id

### g) **communication_preferences**
- **Campos:**
  - id (PK, uuid)
  - user_id (uuid, FK → auth.users.id)
  - email_notifications, sms_notifications, whatsapp_notifications, location_sharing_enabled, emergency_contact_email, emergency_contact_phone, created_at, updated_at
- **Relacionamentos:**
  - user_id → auth.users.id

---

## 2. Relacionamentos-Chave
- **Usuário (auth.users)** pode ter:
  - 1 perfil em `profiles`
  - 1 ou mais vínculos em `guardians` (como responsável)
  - 1 ou mais vínculos em `students` (como estudante)
  - 1 ou mais localizações em `locations` e `location_history`
  - Preferências em `communication_preferences`
- **Estudante** pode ter múltiplos responsáveis via `student_guardian_relationships`.
- **Responsável** pode estar vinculado a múltiplos estudantes.

---

## 3. Extensões e Funcionalidades Avançadas
- **PostGIS**: Suporte a dados geoespaciais (localização).
- **pgcrypto, pgsodium**: Criptografia e segurança de dados.
- **pgjwt**: Autenticação baseada em JWT.
- **pg_stat_statements, pg_stat_monitor**: Monitoramento de queries e performance.
- **pg_graphql**: Suporte a queries GraphQL.
- **Supabase Vault**: Gerenciamento seguro de segredos.

---

## 4. Migrations Recentes Importantes
- Diversas migrations recentes focadas em:
  - Políticas RLS (Row Level Security) para garantir privacidade e acesso seguro.
  - Funções RPC para busca de localizações e relacionamentos.
  - Melhorias em triggers, permissões e histórico de localização.

---

## 5. Observações de Segurança
- **RLS habilitado** nas principais tabelas (profiles, guardians, locations, etc).
- **Políticas de acesso** revisadas e aprimoradas em migrations recentes.
- **Relacionamentos explícitos** entre usuários, estudantes e responsáveis garantem integridade e rastreabilidade.

---

## 6. Resumo Visual (ERD Simplificado)

- **auth.users** ←1---1→ **profiles**
- **auth.users** ←1---n→ **students**
- **auth.users** ←1---n→ **guardians**
- **students** ←n---n→ **guardians** (via student_guardian_relationships)
- **auth.users** ←1---n→ **locations**
- **auth.users** ←1---n→ **location_history**

---

## Erro Reportado

```
POST https://rsvjnndhbyyxktbczlnk.supabase.co/rest/v1/rpc/get_student_locations_for_guardian 404 (Not Found)
LocationService.ts:14 Error fetching student locations: 
{code: 'PGRST202', details: 'Searched for the function public.get_student_locations_for_guardian, but no matches were found in the schema cache.', hint: 'Perhaps you meant to call the function public.get_student_locations_for_guardian(input_guardian_id)', message: 'Could not find the function public.get_student_locations_for_guardian(p_student_id) in the schema cache'}
```

---

## Análise do Erro

### Código do Erro: PGRST202
- **Tipo:** Função não encontrada
- **Status HTTP:** 404 (Not Found)
- **Origem:** PostgREST (API REST do Supabase)

### Causa Raiz
O erro **NÃO** indica que a função não existe no banco de dados. O problema é que:

- O **nome do parâmetro** usado na chamada RPC do frontend **não coincide** com o nome definido na função SQL.
- O Supabase/PostgREST exige que os nomes dos parâmetros sejam **idênticos** aos definidos na assinatura da função.

### Evidência
A mensagem de erro mostra claramente:
- **Chamada feita:** `get_student_locations_for_guardian(p_student_id)`
- **Função esperada:** `get_student_locations_for_guardian(input_guardian_id)`

---

## Diagnóstico

### 1. Verificar a Função no Banco
A função `get_student_locations_for_guardian` existe e está definida como:
```sql
CREATE OR REPLACE FUNCTION public.get_student_locations_for_guardian(input_guardian_id uuid)
RETURNS TABLE (...);
```

### 2. Verificar a Chamada no Frontend
O frontend está chamando com parâmetro incorreto:
```ts
// ❌ INCORRETO
const { data, error } = await supabase.rpc('get_student_locations_for_guardian', {
  p_student_id: guardianId  // Nome errado!
});
```

### 3. Identificar a Discrepância
- **Função SQL espera:** `input_guardian_id`
- **Frontend envia:** `p_student_id`
- **Resultado:** 404 PGRST202

---

## Solução

### Corrigir a Chamada no Frontend
```ts
// ✅ CORRETO
const { data, error } = await supabase.rpc('get_student_locations_for_guardian', {
  input_guardian_id: guardianId  // Nome correto!
});
```

### Exemplo Completo de Correção

**LocationService.ts (ANTES - Incorreto):**
```ts
export const getStudentLocationsForGuardian = async (guardianId: string) => {
  const { data, error } = await supabase.rpc('get_student_locations_for_guardian', {
    p_student_id: guardianId  // ❌ Nome errado
  });
  
  if (error) {
    console.error('Error fetching student locations:', error);
    throw error;
  }
  
  return data;
};
```

**LocationService.ts (DEPOIS - Correto):**
```ts
export const getStudentLocationsForGuardian = async (guardianId: string) => {
  const { data, error } = await supabase.rpc('get_student_locations_for_guardian', {
    input_guardian_id: guardianId  // ✅ Nome correto
  });
  
  if (error) {
    console.error('Error fetching student locations:', error);
    throw error;
  }
  
  return data;
};
```

---

## Validação da Correção

### Checklist
- [ ] Nome do parâmetro na chamada RPC é exatamente igual ao da função SQL
- [ ] Função existe no painel Supabase > SQL Editor > Functions
- [ ] Cache do schema foi atualizado (aguardar alguns minutos se necessário)
- [ ] Teste da chamada no frontend após correção

### Como Testar
1. **No Supabase SQL Editor:**
   ```sql
   SELECT * FROM get_student_locations_for_guardian('uuid-do-guardian-aqui');
   ```

2. **No Frontend (console do navegador):**
   ```ts
   const result = await supabase.rpc('get_student_locations_for_guardian', {
     input_guardian_id: 'uuid-do-guardian-aqui'
   });
   console.log(result);
   ```

---

## Prevenção

### Boas Práticas
1. **Sempre verificar a assinatura da função** antes de chamá-la
2. **Usar nomes de parâmetros descritivos e consistentes**
3. **Documentar as funções RPC** com exemplos de chamada
4. **Implementar tipagem TypeScript** para as funções RPC

### Exemplo de Tipagem TypeScript
```ts
type GetStudentLocationsParams = {
  input_guardian_id: string;
};

type StudentLocationResult = {
  student_id: string;
  student_name: string;
  location_id: string;
  latitude: number;
  longitude: number;
  location_timestamp: string;
};

export const getStudentLocationsForGuardian = async (
  params: GetStudentLocationsParams
): Promise<StudentLocationResult[]> => {
  const { data, error } = await supabase.rpc(
    'get_student_locations_for_guardian',
    params
  );
  
  if (error) throw error;
  return data || [];
};
```

---

## Resumo

- **Erro:** 404 PGRST202 ao chamar função RPC
- **Causa:** Nome do parâmetro incorreto na chamada
- **Solução:** Usar `input_guardian_id` em vez de `p_student_id`
- **Prevenção:** Verificar assinatura da função e usar tipagem TypeScript

**Status:** ✅ Resolvido após correção do nome do parâmetro na chamada RPC.

---

*Relatório gerado em: Janeiro 2025*
*Projeto: Locate-Family-Connect*
*Banco: Supabase (rsvjnndhbyyxktbczlnk)* 