---
description: Redis Cache & Notification System
---

## Overview

Redis serves two critical functions in this system:
1. **Caching Layer** - Optimizes database query performance
2. **Real-Time Notifications** - Feeds updates to the dashboard of the responsible

## 🔄 Cache Management Workflow

### Cache Initialization
```bash
# Check if the Redis service is running
redis-cli ping

# Check current cache size
redis-cli info | grep used_memory_human

# Clear cache if necessary (use with caution)
redis-cli flushdb
```

### Cache Monitoring
```bash
# Monitor hit rate
redis-cli info stats | grep hit_rate

# Check cache keys for student location data
redis-cli keys "student:location:*"

# Examine key expiration
redis-cli ttl "student:location:{id}"
```

## 📱 Real-Time Notifications Workflow

### Testing the Notifications Pipeline
```bash
# Publish test notification
redis-cli publish "guardian:notifications" "{\"type\":\"location_update\",\"studentId\":\"test-id\",\"timestamp\":\"$(date -u +\"%Y-%m-%dT%H:%M:%SZ\")\"}"

# Monitor notification channel
redis-cli psubscribe "guardian:*"
```

### Baseline Performance
```bash
# Capture notification latency metrics
npm run performance-baseline -- --component=redis-notifications

# Validate notification delivery
npm run critical-test -- --test=notification-delivery
```

## 🛠️ Troubleshooting

### Connection Issues
```bash
# Check Redis connection from application
node scripts/test-redis-connection.js

# Check Redis connection configuration
grep REDIS_ .env
```

### Memory Issues
```bash
# Check memory usage
redis-cli info memory

# Identify large keys
redis-cli --bigkeys
```

## 🔒 Security Protocols

1. **Before any changes to Redis configuration:**
```bash
# Run health check
npm run health-check

# Take memory snapshot
redis-cli bgsave
```

2. **After changes:**
```bash
# Check critical functionality
npm run critical-test -- --component=redis

# Compare performance metrics
npm run performance-baseline -- --compare
```

## 📊 Implementation Patterns

### Cache-Aside (Currently Implemented)
1. Check cache first (Cache Hit?)
2. If miss, query database
3. Update cache with query results
4. Return data to client

### Write-Through Cache
For future implementation to ensure cache consistency:
```javascript
// Example implementation
async function updateStudentLocation(studentId, location) {
// Update DB first
await supabaseClient.from('locations').upsert({
student_id: studentId,
latitude: location.latitude,
longitude: location.longitude,
timestamp: new Date()
});

// Then refresh cache
await redisClient.set(
`student:location:${studentId}`,
JSON.stringify(location),
'EX',
60 * 15 // 15 minute expiration
);

// Publish notification
await redisClient.publish(
'guardian:notifications',
JSON.stringify({
type: 'location_update',
studentId,
timestamp: new Date()
})
); }
```

## 📝 Health Check Commands

```bash
# Check Redis server health
npm run health-check -- --component=redis

# Test cache functionality
npm run critical-test -- --test=redis-cache

# Test notification functionality
npm run critical-test -- --test=redis-notifications

# Run full Redis check
npm run pre-deploy -- --focus=redis
```

Remember: "EVERY change must be PROVEN SAFE before being applied"